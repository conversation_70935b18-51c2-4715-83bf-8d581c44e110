/**
 * Vault Loading Diagnostics Test Suite
 * 
 * This test suite identifies and validates the root causes of vault and folder
 * scanning issues in ChatLo. It tests initialization sequences, state management,
 * path resolution, and cache management.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { vaultUIManager } from '../src/services/vaultUIManager'
import { contextVaultService } from '../src/services/contextVaultService'
import { unifiedPathService } from '../src/services/unifiedPathService'
import { cacheManager } from '../src/services/cacheManager'

// Mock electron APIs
const mockElectronAPI = {
  vault: {
    readDirectory: vi.fn(),
    readFile: vi.fn(),
    writeFile: vi.fn(),
    pathExists: vi.fn()
  },
  settings: {
    get: vi.fn(),
    set: vi.fn()
  }
}

// @ts-ignore
global.window = { electronAPI: mockElectronAPI }

describe('Vault Loading Diagnostics', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset service states
    contextVaultService.clearCache()
    cacheManager.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('1. Race Condition Detection', () => {
    it('should detect initialization race conditions', async () => {
      const initTimes: number[] = []
      const operations: string[] = []

      // Simulate concurrent initialization
      const promises = [
        vaultUIManager.getVaultRegistry().then(() => {
          initTimes.push(Date.now())
          operations.push('vaultRegistry')
        }),
        unifiedPathService.initialize({} as any).then(() => {
          initTimes.push(Date.now())
          operations.push('pathService')
        }),
        contextVaultService.loadVaults().then(() => {
          initTimes.push(Date.now())
          operations.push('contextService')
        })
      ]

      await Promise.allSettled(promises)

      // Check if operations completed in expected order
      const expectedOrder = ['vaultRegistry', 'pathService', 'contextService']
      const actualOrder = operations

      console.log('🔍 [RACE-TEST] Expected order:', expectedOrder)
      console.log('🔍 [RACE-TEST] Actual order:', actualOrder)
      console.log('🔍 [RACE-TEST] Timing differences:', initTimes.map((t, i) => i > 0 ? t - initTimes[0] : 0))

      // This test will likely fail, revealing the race condition
      expect(actualOrder).toEqual(expectedOrder)
    })

    it('should measure initialization timing consistency', async () => {
      const runs = 5
      const timings: number[][] = []

      for (let i = 0; i < runs; i++) {
        const startTime = Date.now()
        const runTimings: number[] = []

        try {
          await vaultUIManager.getVaultRegistry()
          runTimings.push(Date.now() - startTime)

          await unifiedPathService.initialize({} as any)
          runTimings.push(Date.now() - startTime)

          await contextVaultService.loadVaults()
          runTimings.push(Date.now() - startTime)

          timings.push(runTimings)
        } catch (error) {
          console.error(`🔍 [TIMING-TEST] Run ${i + 1} failed:`, error)
          timings.push([0, 0, 0]) // Mark as failed
        }
      }

      console.log('🔍 [TIMING-TEST] Initialization timings across runs:', timings)

      // Check for consistency (variance should be low)
      const variances = [0, 1, 2].map(step => {
        const stepTimes = timings.map(run => run[step]).filter(t => t > 0)
        const avg = stepTimes.reduce((a, b) => a + b, 0) / stepTimes.length
        const variance = stepTimes.reduce((sum, time) => sum + Math.pow(time - avg, 2), 0) / stepTimes.length
        return variance
      })

      console.log('🔍 [TIMING-TEST] Timing variances:', variances)
      
      // High variance indicates inconsistent initialization
      expect(variances.every(v => v < 1000)).toBe(true) // Less than 1 second variance
    })
  })

  describe('2. State Synchronization Issues', () => {
    it('should detect state desynchronization between services', async () => {
      // Mock different states in different services
      mockElectronAPI.vault.readDirectory.mockResolvedValue({
        success: true,
        items: [
          { name: 'test-vault', isDirectory: true, path: '/test/vault1' },
          { name: 'another-vault', isDirectory: true, path: '/test/vault2' }
        ]
      })

      // Load vaults in contextVaultService
      const contextVaults = await contextVaultService.loadVaults()
      
      // Get registry from vaultUIManager
      const registry = await vaultUIManager.getVaultRegistry()

      console.log('🔍 [STATE-TEST] Context vaults:', contextVaults?.length || 0)
      console.log('🔍 [STATE-TEST] Registry vaults:', registry?.vaults?.length || 0)

      // Check if states are synchronized
      const contextVaultCount = contextVaults?.length || 0
      const registryVaultCount = registry?.vaults?.length || 0

      if (contextVaultCount !== registryVaultCount) {
        console.error('🚨 [STATE-TEST] State desynchronization detected!')
        console.error('🚨 [STATE-TEST] Context service has', contextVaultCount, 'vaults')
        console.error('🚨 [STATE-TEST] Registry has', registryVaultCount, 'vaults')
      }

      expect(contextVaultCount).toBe(registryVaultCount)
    })

    it('should detect cache inconsistencies', async () => {
      const cacheKey = 'test-vault-registry'
      const testData = { vaults: [], version: '1.0' }

      // Set cache data
      await cacheManager.set(cacheKey, testData)

      // Get cached data
      const cachedData = await cacheManager.get(cacheKey)

      console.log('🔍 [CACHE-TEST] Original data:', testData)
      console.log('🔍 [CACHE-TEST] Cached data:', cachedData)

      // Check cache integrity
      expect(cachedData).toEqual(testData)

      // Test cache invalidation
      await cacheManager.clear()
      const clearedData = await cacheManager.get(cacheKey)

      console.log('🔍 [CACHE-TEST] Data after clear:', clearedData)
      expect(clearedData).toBeNull()
    })
  })

  describe('3. Path Resolution Issues', () => {
    it('should detect path resolution failures', async () => {
      const testPaths = [
        '/valid/absolute/path',
        'relative/path',
        'C:\\Windows\\Path',
        '/corrupted/path/with/undefined',
        '',
        null as any,
        undefined as any
      ]

      const results = []

      for (const path of testPaths) {
        try {
          const result = await unifiedPathService.resolvePath(path, 'test-context')
          results.push({ path, success: result.success, error: result.error })
          console.log('🔍 [PATH-TEST] Path:', path, '→', result.success ? 'SUCCESS' : 'FAILED')
        } catch (error) {
          results.push({ path, success: false, error: error.message })
          console.log('🔍 [PATH-TEST] Path:', path, '→ EXCEPTION:', error.message)
        }
      }

      console.log('🔍 [PATH-TEST] Resolution results:', results)

      // Check if valid paths are being rejected
      const validPaths = results.filter(r => r.path && typeof r.path === 'string' && r.path.length > 0)
      const successfulPaths = validPaths.filter(r => r.success)

      console.log('🔍 [PATH-TEST] Valid paths:', validPaths.length)
      console.log('🔍 [PATH-TEST] Successful resolutions:', successfulPaths.length)

      // At least some valid paths should resolve successfully
      expect(successfulPaths.length).toBeGreaterThan(0)
    })
  })

  describe('4. Error Handling and Recovery', () => {
    it('should test error propagation and recovery', async () => {
      // Simulate various error conditions
      const errorScenarios = [
        { name: 'Network timeout', setup: () => mockElectronAPI.vault.readDirectory.mockRejectedValue(new Error('TIMEOUT')) },
        { name: 'Permission denied', setup: () => mockElectronAPI.vault.readDirectory.mockRejectedValue(new Error('EACCES')) },
        { name: 'Path not found', setup: () => mockElectronAPI.vault.readDirectory.mockRejectedValue(new Error('ENOENT')) },
        { name: 'Invalid response', setup: () => mockElectronAPI.vault.readDirectory.mockResolvedValue({ success: false, error: 'Invalid data' }) }
      ]

      const recoveryResults = []

      for (const scenario of errorScenarios) {
        console.log('🔍 [ERROR-TEST] Testing scenario:', scenario.name)
        scenario.setup()

        try {
          const result = await vaultUIManager.getVaultRegistry()
          recoveryResults.push({ scenario: scenario.name, recovered: !!result, result })
          console.log('🔍 [ERROR-TEST] Scenario', scenario.name, '→', result ? 'RECOVERED' : 'FAILED')
        } catch (error) {
          recoveryResults.push({ scenario: scenario.name, recovered: false, error: error.message })
          console.log('🔍 [ERROR-TEST] Scenario', scenario.name, '→ EXCEPTION:', error.message)
        }
      }

      console.log('🔍 [ERROR-TEST] Recovery results:', recoveryResults)

      // Check if any scenarios have proper recovery mechanisms
      const recoveredScenarios = recoveryResults.filter(r => r.recovered)
      console.log('🔍 [ERROR-TEST] Scenarios with recovery:', recoveredScenarios.length, '/', errorScenarios.length)

      // At least some error scenarios should have recovery mechanisms
      expect(recoveredScenarios.length).toBeGreaterThan(0)
    })
  })

  describe('5. Performance and Memory Issues', () => {
    it('should detect memory leaks in repeated operations', async () => {
      const initialMemory = process.memoryUsage()
      console.log('🔍 [MEMORY-TEST] Initial memory:', initialMemory)

      // Perform repeated operations
      for (let i = 0; i < 100; i++) {
        try {
          await vaultUIManager.getVaultRegistry()
          await contextVaultService.loadVaults()
          
          // Force garbage collection if available
          if (global.gc) {
            global.gc()
          }
        } catch (error) {
          console.warn('🔍 [MEMORY-TEST] Operation', i, 'failed:', error.message)
        }
      }

      const finalMemory = process.memoryUsage()
      console.log('🔍 [MEMORY-TEST] Final memory:', finalMemory)

      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      console.log('🔍 [MEMORY-TEST] Memory increase:', memoryIncrease, 'bytes')

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
    })
  })
})
