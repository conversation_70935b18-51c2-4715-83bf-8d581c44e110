# Development Diagnostics Setup Guide

**Purpose:** Enable development diagnostics to monitor vault loading and identify issues in real-time.

## 🔧 **Quick Setup Instructions**

### **Method 1: Environment Variable (Recommended)**

1. **For Windows (PowerShell):**
   ```powershell
   $env:NODE_ENV="development"
   npm run dev
   ```

2. **For Windows (Command Prompt):**
   ```cmd
   set NODE_ENV=development
   npm run dev
   ```

3. **For macOS/Linux:**
   ```bash
   NODE_ENV=development npm run dev
   ```

### **Method 2: Package.json Script (Permanent)**

Add this to your `package.json` scripts section:
```json
{
  "scripts": {
    "dev:debug": "NODE_ENV=development npm run dev",
    "dev:diagnostics": "NODE_ENV=development npm run dev"
  }
}
```

Then run:
```bash
npm run dev:debug
```

### **Method 3: Manual Code Toggle**

If environment variables don't work, you can temporarily enable diagnostics by editing `src/pages/FilesPage.tsx`:

```typescript
// Find this line (around line 169):
if (process.env.NODE_ENV === 'development') {

// Replace with:
if (true) { // TEMP: Force enable diagnostics
```

**Remember to revert this change before committing!**

---

## 🔍 **What to Look For**

Once diagnostics are enabled, you'll see detailed console output like this:

### **Successful Initialization:**
```
[FILES] 🚀 Starting FilesPage initialization sequence...
[FILES] Step 1: Initializing path service...
[FILES] ✅ Path service initialized
[FILES] Step 2: Loading file tree...
[FILES] ✅ File tree loaded
[FILES] 🎉 FilesPage initialization completed successfully
[FILES] 🔍 Running vault loading diagnostics...
[FILES] ✅ No vault loading issues detected
```

### **Issues Detected:**
```
[FILES] 🚨 Vault loading issues detected: [
  "2 operations failed",
  "Path service initialized before vault registry loaded (race condition)"
]
[FILES] 💡 Recommendations: [
  "Check error logs and implement proper error handling",
  "Ensure proper initialization order: registry → path service → context service"
]
```

### **PathResolver Fix Verification:**
Look for successful path operations instead of the previous error:
```
✅ SUCCESS: Path operations working
❌ BEFORE: [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function'}
```

---

## 🧪 **Running Diagnostic Tests**

### **1. Test the PathResolver Fix:**
```bash
node scripts/test-path-join-fix.js
```

Expected output:
```
🔧 [PATH-FIX-TEST] ✅ All tests passed! The fix should work correctly.
```

### **2. Run Integration Diagnostics:**
```bash
node scripts/run-vault-diagnostics.js --quick
```

### **3. Start Real-time Monitoring:**
Add this button to your UI temporarily for testing:
```typescript
<button 
  onClick={() => {
    import('../utils/vaultLoadingDiagnostics').then(({ startLoadingMonitor }) => {
      startLoadingMonitor(1000, 30000) // Monitor for 30 seconds
    })
  }}
  className="px-3 py-1 bg-blue-500 text-white rounded text-xs"
>
  Start Vault Monitoring
</button>
```

---

## 🐛 **Troubleshooting**

### **Issue: Environment Variable Not Working**
**Solution:** Use Method 3 (manual code toggle) temporarily.

### **Issue: No Diagnostic Output**
**Possible Causes:**
1. `NODE_ENV` not set correctly
2. Browser cache preventing code updates
3. Build process not picking up changes

**Solutions:**
1. Clear browser cache and reload
2. Restart the development server
3. Check browser console for any JavaScript errors

### **Issue: Too Much Console Output**
**Solution:** Filter console messages by typing `[FILES]` or `[DIAGNOSTICS]` in the browser console filter.

### **Issue: Diagnostics Causing Performance Issues**
**Solution:** The diagnostics are designed to be lightweight, but if needed, you can disable specific parts by commenting out sections in `src/utils/vaultLoadingDiagnostics.ts`.

---

## 📊 **Understanding Diagnostic Reports**

### **Loading Sequence Report Structure:**
```typescript
{
  totalDuration: 1250,        // Total time in milliseconds
  operations: [               // Individual operation results
    {
      timestamp: 0,
      operation: 'VaultRegistry Loading',
      success: true,
      duration: 450,
      details: { vaultCount: 2 }
    }
  ],
  issues: [                   // Problems detected
    "2 operations failed"
  ],
  recommendations: [          // Suggested fixes
    "Check error logs and implement proper error handling"
  ]
}
```

### **Common Issues and Meanings:**

| Issue | Meaning | Action |
|-------|---------|--------|
| "Operations failed" | Some vault operations didn't complete | Check network, file permissions |
| "Race condition detected" | Initialization order problem | Fixed in our updates |
| "Path resolution failures" | PathResolver issues | Fixed with joinSafe correction |
| "State desynchronization" | Services have inconsistent data | Restart app, check for errors |

---

## 🎯 **Next Steps After Setup**

1. **Enable diagnostics** using one of the methods above
2. **Open FilesPage** in your app
3. **Check console output** for diagnostic reports
4. **Look for the PathResolver fix** - you should no longer see the `joinPaths is not a function` error
5. **Monitor vault loading** - operations should complete successfully
6. **Report any remaining issues** with the diagnostic output

The diagnostics will help identify any remaining issues and provide specific recommendations for fixes.
