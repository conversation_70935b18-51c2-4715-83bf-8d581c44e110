/**
 * Unified API Client
 * Provides a clean, typed interface for all backend API calls
 * Replaces direct electronAPI usage with a structured approach
 */

// Remove duplicate declaration - already defined in types/index.ts

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp?: string
}

export interface APICallOptions {
  timeout?: number
  retries?: number
  retryDelay?: number
  validateResponse?: boolean
}

export class APIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public category?: string,
    public endpoint?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class UnifiedAPIClient {
  private static instance: UnifiedAPIClient
  private defaultTimeout = 30000 // 30 seconds
  private defaultRetries = 3
  private defaultRetryDelay = 1000 // 1 second

  private constructor() {}

  static getInstance(): UnifiedAPIClient {
    if (!UnifiedAPIClient.instance) {
      UnifiedAPIClient.instance = new UnifiedAPIClient()
    }
    return UnifiedAPIClient.instance
  }

  /**
   * Generic API call method with error handling and retries
   */
  public async call<T = any>(
    category: string,
    endpoint: string,
    args: any[] = [],
    options: APICallOptions = {}
  ): Promise<T> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = this.defaultRetryDelay,
      validateResponse = true
    } = options

    const channelName = `${category}:${endpoint}`
    let lastError: Error

    // [IPC-LOG] Log all API calls for debugging
    console.log('[IPC-LOG] 📡 API Call:', {
      channelName,
      category,
      endpoint,
      argsCount: args.length,
      args: args.map(arg => typeof arg === 'object' ? `${typeof arg}(${Object.keys(arg).length} keys)` : `${typeof arg}(${String(arg).substring(0, 50)})`),
      timestamp: new Date().toISOString()
    });

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new APIError(`API call timeout: ${channelName}`, 'TIMEOUT', category, endpoint)), timeout)
        })

        // Make the API call
        const apiPromise = window.electronAPI.invoke(channelName, ...args)
        const result = await Promise.race([apiPromise, timeoutPromise])

        // [IPC-LOG] Log successful responses
        console.log('[IPC-LOG] ✅ API Response:', {
          channelName,
          success: result?.success,
          hasData: !!result?.data,
          hasError: !!result?.error,
          responseType: typeof result,
          timestamp: new Date().toISOString()
        });

        // Validate response if requested
        if (validateResponse && result && typeof result === 'object') {
          if (result.success === false) {
            console.log('[IPC-LOG] ❌ API Error Response:', {
              channelName,
              error: result.error,
              code: result.code
            });
            throw new APIError(result.error || 'API call failed', result.code, category, endpoint)
          }
        }

        return result
      } catch (error: any) {
        lastError = error
        
        // Don't retry on certain errors
        if (error.code === 'TIMEOUT' || error.code === 'VALIDATION_ERROR' || attempt === retries) {
          break
        }

        // Wait before retry
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
        }
      }
    }

    throw lastError!
  }

  // Database APIs
  async getConversations(): Promise<any[]> {
    return this.call('db', 'getConversations')
  }

  async getConversation(id: string): Promise<any> {
    return this.call('db', 'getConversation', [id])
  }

  async createConversation(title: string): Promise<any> {
    return this.call('db', 'createConversation', [title])
  }

  async updateConversation(id: string, title: string): Promise<any> {
    return this.call('db', 'updateConversation', [id, title])
  }

  async deleteConversation(id: string): Promise<any> {
    return this.call('db', 'deleteConversation', [id])
  }

  async getMessages(conversationId: string): Promise<any[]> {
    return this.call('db', 'getMessages', [conversationId])
  }

  async addMessage(conversationId: string, message: any): Promise<any> {
    return this.call('db', 'addMessage', [conversationId, message])
  }

  async getFiles(): Promise<any[]> {
    return this.call('db', 'getFiles')
  }

  async addFile(file: any): Promise<any> {
    return this.call('db', 'addFile', [file])
  }

  async updateFile(id: string, updates: any): Promise<any> {
    return this.call('db', 'updateFile', [id, updates])
  }

  async deleteFile(id: string): Promise<any> {
    return this.call('db', 'deleteFile', [id])
  }

  // Vault APIs
  async createDirectory(dirPath: string): Promise<APIResponse> {
    return this.call('vault', 'createDirectory', [dirPath])
  }

  async writeFile(filePath: string, content: string): Promise<APIResponse> {
    return this.call('vault', 'writeFile', [filePath, content])
  }

  async readFile(filePath: string): Promise<APIResponse<string>> {
    return this.call('vault', 'readFile', [filePath])
  }

  async readDirectory(dirPath: string): Promise<APIResponse<any[]>> {
    return this.call('vault', 'readDirectory', [dirPath])
  }

  async removeDirectory(dirPath: string): Promise<APIResponse> {
    return this.call('vault', 'removeDirectory', [dirPath])
  }

  async removeFile(filePath: string): Promise<APIResponse> {
    return this.call('vault', 'removeFile', [filePath])
  }

  async copyFile(sourcePath: string, destinationPath: string): Promise<APIResponse> {
    return this.call('vault', 'copyFile', [sourcePath, destinationPath])
  }

  async pathExists(targetPath: string): Promise<APIResponse<boolean>> {
    return this.call('vault', 'pathExists', [targetPath])
  }

  async getVaultRegistry(): Promise<any> {
    return this.call('vault', 'getVaultRegistry')
  }

  async saveVaultRegistry(registry: any): Promise<APIResponse> {
    return this.call('vault', 'saveVaultRegistry', [registry])
  }

  // Files APIs
  async indexFile(filePath: string, forceReindex: boolean = false): Promise<APIResponse> {
    return this.call('files', 'indexFile', [filePath, forceReindex])
  }

  async addFileAttachment(messageId: string, fileId: string, attachmentType: 'attachment' | 'reference'): Promise<APIResponse> {
    return this.call('files', 'addFileAttachment', [messageId, fileId, attachmentType])
  }

  // Additional Files APIs for ChatArea compliance
  async getIndexedFiles(): Promise<APIResponse<any[]>> {
    return this.call('files', 'getIndexedFiles')
  }

  async processFile(filePath: string, options?: any): Promise<APIResponse> {
    return this.call('files', 'processFile', [filePath, options])
  }

  async processFileContent(filePath: string, options?: any): Promise<APIResponse> {
    return this.call('files', 'processFileContent', [filePath, options])
  }

  async showOpenDialog(options: any): Promise<APIResponse> {
    return this.call('files', 'showOpenDialog', [options])
  }

  async showSaveDialog(options: any): Promise<APIResponse> {
    return this.call('files', 'showSaveDialog', [options])
  }

  async saveContentAsFile(content: string, filePath: string): Promise<APIResponse> {
    return this.call('files', 'saveContentAsFile', [content, filePath])
  }

  async saveContentToVault(content: string, fileName: string, contextPath: string): Promise<APIResponse> {
    return this.call('files', 'saveContentToVault', [content, fileName, contextPath])
  }

  // Plugin APIs
  async getAllPlugins(): Promise<any[]> {
    return this.call('plugins', 'getAll')
  }

  async enablePlugin(pluginId: string, enabled: boolean): Promise<void> {
    return this.call('plugins', 'enable', [pluginId, enabled])
  }

  async disablePlugin(pluginId: string): Promise<void> {
    return this.call('plugins', 'disable', [pluginId])
  }

  async getPluginConfig(pluginId: string): Promise<any> {
    return this.call('plugins', 'getConfig', [pluginId])
  }

  async updatePluginConfig(pluginId: string, config: any): Promise<void> {
    return this.call('plugins', 'updateConfig', [pluginId, config])
  }

  async getPluginAPIEndpoints(pluginId: string): Promise<APIResponse<any>> {
    return this.call('plugins', 'getAPIEndpoints', [pluginId])
  }

  async getAllPluginAPIEndpoints(): Promise<APIResponse<any[]>> {
    return this.call('plugins', 'getAllAPIEndpoints')
  }

  // System APIs
  async getAPIRegistry(): Promise<APIResponse<any>> {
    return this.call('system', 'getAPIRegistry')
  }

  async getPerformanceMetrics(): Promise<APIResponse<any>> {
    return this.call('system', 'getPerformanceMetrics')
  }

  async getMonitoringData(): Promise<APIResponse<any>> {
    return this.call('system', 'getMonitoringData')
  }

  async getEndpointMetrics(category: string, endpoint: string): Promise<APIResponse<any>> {
    return this.call('system', 'getEndpointMetrics', [category, endpoint])
  }

  async resetMonitoring(): Promise<APIResponse> {
    return this.call('system', 'resetMonitoring')
  }

  async cleanupMiddleware(): Promise<APIResponse> {
    return this.call('system', 'cleanupMiddleware')
  }

  // Updater APIs
  async checkForUpdates(): Promise<APIResponse> {
    return this.call('updater', 'checkForUpdates')
  }

  async downloadUpdate(): Promise<APIResponse> {
    return this.call('updater', 'downloadUpdate')
  }

  async installUpdate(): Promise<APIResponse> {
    return this.call('updater', 'installUpdate')
  }

  // Generic plugin API call
  async callPluginAPI(pluginId: string, endpoint: string, ...args: any[]): Promise<any> {
    const channelName = `plugin_${pluginId}:${endpoint}`
    return window.electronAPI.invoke(channelName, ...args)
  }

  // Batch API calls
  async batchCall<T = any>(calls: Array<{
    category: string
    endpoint: string
    args?: any[]
    options?: APICallOptions
  }>): Promise<Array<{ success: boolean; data?: T; error?: string }>> {
    const promises = calls.map(async call => {
      try {
        const data = await this.call<T>(call.category, call.endpoint, call.args, call.options)
        return { success: true, data }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    return Promise.all(promises)
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; latency: number; errors: string[] }> {
    const startTime = Date.now()
    const errors: string[] = []
    let healthy = true

    try {
      // Test basic API connectivity
      await this.call('system', 'getAPIRegistry', [], { timeout: 5000, retries: 1 })
    } catch (error: any) {
      healthy = false
      errors.push(`API connectivity failed: ${error.message}`)
    }

    const latency = Date.now() - startTime

    return { healthy, latency, errors }
  }

  // Configuration
  setDefaultTimeout(timeout: number): void {
    this.defaultTimeout = timeout
  }

  setDefaultRetries(retries: number): void {
    this.defaultRetries = retries
  }

  setDefaultRetryDelay(delay: number): void {
    this.defaultRetryDelay = delay
  }
}

// Export singleton instance
export const apiClient = UnifiedAPIClient.getInstance()

// Export convenience methods for common patterns
export const db = {
  conversations: {
    getAll: () => apiClient.getConversations(),
    get: (id: string) => apiClient.getConversation(id),
    create: (title: string) => apiClient.createConversation(title),
    update: (id: string, title: string) => apiClient.updateConversation(id, title),
    delete: (id: string) => apiClient.deleteConversation(id)
  },
  messages: {
    get: (conversationId: string) => apiClient.getMessages(conversationId),
    add: (conversationId: string, message: any) => apiClient.addMessage(conversationId, message)
  },
  files: {
    getAll: () => apiClient.getFiles(),
    add: (file: any) => apiClient.addFile(file),
    update: (id: string, updates: any) => apiClient.updateFile(id, updates),
    delete: (id: string) => apiClient.deleteFile(id)
  },
  // Direct database methods
  createConversation: (title: string) => apiClient['call']('db', 'createConversation', [title] as any),
  getConversations: () => apiClient['call']('db', 'getConversations', [] as any),
  getConversationsWithArtifacts: () => apiClient['call']('db', 'getConversationsWithArtifacts', [] as any),
  getMessages: (conversationId: string) => apiClient['call']('db', 'getMessages', [conversationId] as any),
  addMessage: (conversationId: string, message: any) => apiClient['call']('db', 'addMessage', [conversationId, message] as any),
  updateMessageIntelligence: (messageId: string, entities: string, topics: string, confidence: number) =>
    apiClient['call']('db', 'updateMessageIntelligence', [messageId, entities, topics, confidence] as any),
  addPinnedIntelligence: (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) =>
    apiClient['call']('db', 'addPinnedIntelligence', [messageId, extractionData, vaultAssignment, processingMetadata] as any),
  updateConversation: (id: string, title: string) => apiClient['call']('db', 'updateConversation', [id, title] as any),
  deleteConversation: (id: string) => apiClient['call']('db', 'deleteConversation', [id] as any),
  addFileAttachment: (messageId: string, fileId: string, attachmentType: string) => apiClient['call']('db', 'addFileAttachment', [messageId, fileId, attachmentType] as any),
  searchConversations: (term: string) => apiClient['call']('db', 'searchConversations', [term] as any),
  togglePinMessage: (messageId: string) => apiClient['call']('db', 'togglePinMessage', [messageId] as any),
  // Portable mode methods for settings
  connectPortableDB: (portablePath: string) => apiClient['call']('db', 'connectPortableDB', [portablePath] as any),
  prepareForDisconnect: () => apiClient['call']('db', 'prepareForDisconnect', [] as any)
}

export const settings = {
  get: (key: string) => apiClient['call']('settings', 'get', [key] as any),
  set: (key: string, value: any) => apiClient['call']('settings', 'set', [key, value] as any),
  setPortableMode: (enabled: boolean) => apiClient['call']('settings', 'setPortableMode', [enabled] as any)
}

export const vault = {
  createDirectory: (path: string) => apiClient.createDirectory(path),
  writeFile: (path: string, content: string) => apiClient.writeFile(path, content),
  readFile: (path: string) => apiClient.readFile(path),
  readDirectory: (path: string) => apiClient.readDirectory(path),
  removeDirectory: (path: string) => apiClient.removeDirectory(path),
  removeFile: (path: string) => apiClient.removeFile(path),
  copyFile: (sourcePath: string, destinationPath: string) => apiClient.copyFile(sourcePath, destinationPath),
  pathExists: (path: string) => apiClient.pathExists(path),
  getRegistry: () => apiClient.getVaultRegistry(),
  getVaultRegistry: () => apiClient['call']('vault', 'getVaultRegistry', [] as any),
  saveVaultRegistry: (registry: any) => apiClient['call']('vault', 'saveVaultRegistry', [registry] as any),
  initializeVaultRoot: (rootPath: string, name: string) => apiClient['call']('vault', 'initializeVaultRoot', [rootPath, name] as any),
  saveRegistry: (registry: any) => apiClient.saveVaultRegistry(registry)
}

export const files = {
  // Core file operations
  indexFile: (filePath: string, forceReindex: boolean = false) => apiClient.indexFile(filePath, forceReindex),
  addFileAttachment: (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') =>
    apiClient.addFileAttachment(messageId, fileId, attachmentType),

  // File listing and processing
  getIndexedFiles: () => apiClient.getIndexedFiles(),
  processFile: (filePath: string, options?: any) => apiClient.processFile(filePath, options),
  processFileContent: (filePath: string, options?: any) => apiClient.processFileContent(filePath, options),

  // File dialogs
  showOpenDialog: (options: any) => apiClient.showOpenDialog(options),
  showSaveDialog: (options: any) => apiClient.showSaveDialog(options),

  // File saving
  saveContentAsFile: (content: string, filePath: string) => apiClient.saveContentAsFile(content, filePath),
  saveContentToVault: (content: string, fileName: string, contextPath: string) =>
    apiClient.saveContentToVault(content, fileName, contextPath),

  // Message file operations
  getMessageFiles: (messageId: string) => apiClient['call']('files', 'getMessageFiles', [messageId] as any),

  // Path inference
  inferVaultPath: (filePath: string) => apiClient['call']('files', 'inferVaultPath', [filePath] as any),
  setVaultRootPath: (path: string) => apiClient['call']('files', 'setVaultRootPath', [path] as any)
}

export const plugins = {
  getAll: () => apiClient.getAllPlugins(),
  enable: (id: string, enabled: boolean) => apiClient.enablePlugin(id, enabled),
  disable: (id: string) => apiClient.disablePlugin(id),
  getConfig: (id: string) => apiClient.getPluginConfig(id),
  updateConfig: (id: string, config: any) => apiClient.updatePluginConfig(id, config),
  getAPIEndpoints: (id: string) => apiClient.getPluginAPIEndpoints(id),
  getAllAPIEndpoints: () => apiClient.getAllPluginAPIEndpoints(),
  callAPI: (id: string, endpoint: string, ...args: any[]) => apiClient.callPluginAPI(id, endpoint, ...args)
}

// Intelligence client (kernel)
export const intelligence = {
  // Preferred names
  write: (filePath: string, vaultPath: string, payload: { rawMarkdown?: string; json?: any }) =>
    apiClient['call']('intelligence', 'write', [filePath, vaultPath, payload] as any),
  read: (filePath: string, vaultPath: string) =>
    apiClient['call']('intelligence', 'read', [filePath, vaultPath] as any),
  // Legacy aliases
  save: (filePath: string, vaultPath: string, payload: { rawMarkdown?: string; json?: any }) =>
    apiClient['call']('intelligence', 'save', [filePath, vaultPath, payload] as any),
  get: (filePath: string, vaultPath: string) =>
    apiClient['call']('intelligence', 'get', [filePath, vaultPath] as any)
}

export const events = {
  emit: (eventName: string, payload?: any) => apiClient['call']('events', 'emit', [eventName, payload] as any),
  on: (eventName: string, handler: (payload?: any) => void) => apiClient['call']('events', 'on', [eventName, handler] as any),
  subscribe: (category: 'file' | 'intelligence' | 'task', filter?: any) => apiClient['call']('events', 'subscribe', [category, filter] as any),
  unsubscribe: (subscriptionId: string) => apiClient['call']('events', 'unsubscribe', [subscriptionId] as any)
}

export const shell = {
  openPath: (path: string) => apiClient['call']('shell', 'openPath', [path] as any),
  showItemInFolder: (path: string) => apiClient['call']('shell', 'showItemInFolder', [path] as any)
}

export const path = {
  normalize: (path: string) => apiClient['call']('path', 'normalize', [path] as any),
  join: (...paths: string[]) => apiClient['call']('path', 'join', paths as any)
}

export const system = {
  getAPIRegistry: () => apiClient.getAPIRegistry(),
  getPerformanceMetrics: () => apiClient.getPerformanceMetrics(),
  getMonitoringData: () => apiClient.getMonitoringData(),
  getEndpointMetrics: (category: string, endpoint: string) => apiClient.getEndpointMetrics(category, endpoint),
  resetMonitoring: () => apiClient.resetMonitoring(),
  cleanupMiddleware: () => apiClient.cleanupMiddleware(),
  healthCheck: () => apiClient.healthCheck()
}
