{"name": "filesystem", "version": "1.0.0", "description": "Simple filesystem operations - direct replacement for current implementation", "main": "SimpleFilesystemModule.ts", "dependencies": [], "optionalDependencies": [], "category": "core", "loadPriority": 1, "lazy": false, "config": {"maxFileSize": 104857600, "allowedExtensions": [".txt", ".md", ".json", ".js", ".ts", ".py", ".html", ".css"], "indexingEnabled": true, "processingTimeout": 30000}}