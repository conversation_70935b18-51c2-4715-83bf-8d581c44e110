/**
 * Test script for model updates and token limits
 */

// Test 1: Check model manifest loading
async function testModelManifest() {
  console.log('🧪 Testing model manifest loading...')

  try {
    const fs = require('fs')
    const path = require('path')

    const manifestPath = path.join(__dirname, 'public', 'models-manifest.json')
    const manifestData = fs.readFileSync(manifestPath, 'utf8')
    const manifest = JSON.parse(manifestData)

    console.log(`✅ Manifest loaded: v${manifest.version}`)
    console.log(`📊 Statistics:`, {
      total: manifest.statistics.total_models,
      flagship: manifest.statistics.flagship_models,
      free: manifest.statistics.free_models
    })

    return manifest
  } catch (error) {
    console.error('❌ Failed to load manifest:', error)
    return null
  }
}

// Test 2: Check token limits for specific models
function testTokenLimits(manifest) {
  console.log('\n🧪 Testing token limits calculation...')
  
  if (!manifest || !manifest.models) {
    console.error('❌ No manifest or models available')
    return
  }
  
  // Test a few key models
  const testModels = [
    'anthropic/claude-3-opus',
    'openai/chatgpt-4o-latest',
    'google/gemini-2.5-pro-exp',
    'xai/grok-4'
  ]
  
  testModels.forEach(modelId => {
    const model = manifest.models.find(m => m.id === modelId)
    if (model) {
      const maxCompletionTokens = model.top_provider?.max_completion_tokens
      const contextLength = model.context_length
      const calculatedMax = maxCompletionTokens || Math.min(Math.floor(contextLength * 0.75), 100000)
      
      console.log(`📝 ${model.name}:`)
      console.log(`   Context: ${contextLength?.toLocaleString() || 'N/A'}`)
      console.log(`   Max completion: ${maxCompletionTokens?.toLocaleString() || 'N/A'}`)
      console.log(`   Calculated limit: ${calculatedMax.toLocaleString()}`)
    } else {
      console.log(`⚠️  Model not found: ${modelId}`)
    }
  })
}

// Test 3: Simulate model update check
async function testUpdateCheck() {
  console.log('\n🧪 Testing update check logic...')
  
  // Simulate stored version (older)
  const storedVersion = '2025.07.15'
  const manifest = await testModelManifest()
  
  if (manifest) {
    const shouldUpdate = manifest.version !== storedVersion
    console.log(`📅 Stored version: ${storedVersion}`)
    console.log(`📅 Latest version: ${manifest.version}`)
    console.log(`🔄 Should update: ${shouldUpdate ? 'YES' : 'NO'}`)
    
    if (shouldUpdate) {
      console.log('✅ Update logic working correctly')
    }
  }
}

// Test 4: Check for specific token limit issues
function testTokenLimitEdgeCases(manifest) {
  console.log('\n🧪 Testing token limit edge cases...')
  
  if (!manifest || !manifest.models) return
  
  // Find models with very high context lengths
  const highContextModels = manifest.models.filter(m => m.context_length > 100000)
  console.log(`🔍 Found ${highContextModels.length} models with >100K context`)
  
  highContextModels.slice(0, 3).forEach(model => {
    const maxCompletionTokens = model.top_provider?.max_completion_tokens
    const contextLength = model.context_length
    const calculatedMax = maxCompletionTokens || Math.min(Math.floor(contextLength * 0.75), 100000)
    
    console.log(`📝 ${model.name}:`)
    console.log(`   Context: ${contextLength.toLocaleString()}`)
    console.log(`   Max completion: ${maxCompletionTokens?.toLocaleString() || 'N/A'}`)
    console.log(`   UI limit: ${calculatedMax.toLocaleString()}`)
    
    // Check if the old hardcoded 100K limit would be wrong
    if (maxCompletionTokens && maxCompletionTokens < 100000) {
      console.log(`   ⚠️  Old hardcoded 100K would be WRONG (should be ${maxCompletionTokens.toLocaleString()})`)
    }
  })
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Model Update & Token Limit Tests')
  console.log('='.repeat(50))
  
  const manifest = await testModelManifest()
  
  if (manifest) {
    testTokenLimits(manifest)
    testTokenLimitEdgeCases(manifest)
  }
  
  await testUpdateCheck()
  
  console.log('\n✅ All tests completed!')
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  runTests()
} else if (typeof module !== 'undefined' && require.main === module) {
  // Node.js environment
  const fetch = require('node-fetch')
  runTests()
}

// Export for use in other contexts
if (typeof module !== 'undefined') {
  module.exports = { testModelManifest, testTokenLimits, testUpdateCheck, runTests }
}
