/**
 * Simple Settings Module
 * Handles application settings including portable mode configuration
 * 
 * WEEK 4: Settings UI Implementation
 */

import { BaseAPIModule, ModuleDependency } from './BaseAPIModule'

export class SimpleSettingsModule extends BaseAPIModule {
  readonly name = 'settings'
  readonly version = '1.0.0'
  readonly description = 'Simple Settings Module for application configuration'
  readonly dependencies: ModuleDependency[] = []

  private dbManager: any

  protected async onInitialize(): Promise<void> {
    // Import required modules dynamically
    const { DatabaseManager } = await import('../../../database')
    
    this.dbManager = new DatabaseManager()
    
    this.log('info', 'Simple Settings Module initialized successfully')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering simple settings endpoints...')

    // Basic settings operations
    this.registerEndpoint('settings', 'get',
      async (key: string) => {
        try {
          console.log(`[SETTINGS] Getting setting: ${key}`)
          
          // Get setting from database
          const value = this.dbManager.getSetting(key)
          console.log(`[SETTINGS] Retrieved setting ${key}:`, value)
          
          return value
        } catch (error) {
          console.error(`[SETTINGS] Failed to get setting ${key}:`, error)
          throw error
        }
      },
      {
        description: 'Get a setting value by key',
        validator: (key: string) => {
          if (typeof key !== 'string' || !key.trim()) {
            throw new Error('Setting key must be a non-empty string')
          }
        }
      }
    )

    this.registerEndpoint('settings', 'set',
      async (key: string, value: any) => {
        try {
          console.log(`[SETTINGS] Setting ${key} to:`, value)
          
          // Set setting in database
          this.dbManager.setSetting(key, value)
          console.log(`[SETTINGS] Successfully set ${key}`)
          
          return { success: true }
        } catch (error) {
          console.error(`[SETTINGS] Failed to set setting ${key}:`, error)
          throw error
        }
      },
      {
        description: 'Set a setting value by key',
        validator: (key: string, value: any) => {
          if (typeof key !== 'string' || !key.trim()) {
            throw new Error('Setting key must be a non-empty string')
          }
          if (value === undefined || value === null) {
            throw new Error('Setting value cannot be undefined or null')
          }
        }
      }
    )

    // Portable mode specific settings
    this.registerEndpoint('settings', 'setPortableMode',
      async (enabled: boolean) => {
        try {
          console.log(`[SETTINGS] Setting portable mode to: ${enabled}`)
          
          // Set portable mode setting
          this.dbManager.setSetting('portable-mode-enabled', enabled)
          
          // If enabling portable mode, also set the database path
          if (enabled) {
            const vaultRoot = this.dbManager.getSetting('vault-root-path')
            if (vaultRoot) {
              const portablePath = `${vaultRoot}/.chatlo-core/chatlo.db`
              this.dbManager.setSetting('db-path', portablePath)
              console.log(`[SETTINGS] Set portable database path to: ${portablePath}`)
            }
          } else {
            // If disabling portable mode, clear the database path
            this.dbManager.setSetting('db-path', '')
            console.log('[SETTINGS] Cleared portable database path')
          }
          
          console.log(`[SETTINGS] Successfully set portable mode to ${enabled}`)
          return { success: true }
        } catch (error) {
          console.error(`[SETTINGS] Failed to set portable mode:`, error)
          throw error
        }
      },
      {
        description: 'Enable or disable portable mode',
        validator: (enabled: boolean) => {
          if (typeof enabled !== 'boolean') {
            throw new Error('Portable mode enabled must be a boolean')
          }
        }
      }
    )

    this.registerEndpoint('settings', 'setDBPath',
      async (path: string) => {
        try {
          console.log(`[SETTINGS] Setting database path to: ${path}`)
          
          // Set database path setting
          this.dbManager.setSetting('db-path', path)
          console.log(`[SETTINGS] Successfully set database path to: ${path}`)
          
          return { success: true }
        } catch (error) {
          console.error(`[SETTINGS] Failed to set database path:`, error)
          throw error
        }
      },
      {
        description: 'Set the database file path',
        validator: (path: string) => {
          if (typeof path !== 'string' || !path.trim()) {
            throw new Error('Database path must be a non-empty string')
          }
        }
      }
    )

    // Get all settings
    this.registerEndpoint('settings', 'getAll',
      async () => {
        try {
          console.log('[SETTINGS] Getting all settings...')
          
          // Get all settings from database
          const settings: Record<string, any> = {}
          
          // Common settings to retrieve
          const commonKeys = [
            'vault-root-path',
            'portable-mode-enabled',
            'db-path',
            'theme',
            'auto-save',
            'notifications',
            'last-activity',
            'mode-switch-count',
            'last-disconnect-timestamp',
            'last-reconnect-timestamp'
          ]
          
          for (const key of commonKeys) {
            try {
              const value = this.dbManager.getSetting(key)
              if (value !== undefined && value !== null) {
                settings[key] = value
              }
            } catch (error) {
              console.warn(`[SETTINGS] Failed to get setting ${key}:`, error)
            }
          }
          
          console.log(`[SETTINGS] Retrieved ${Object.keys(settings).length} settings`)
          return settings
        } catch (error) {
          console.error('[SETTINGS] Failed to get all settings:', error)
          throw error
        }
      },
      {
        description: 'Get all application settings'
      }
    )

    // Reset settings to defaults
    this.registerEndpoint('settings', 'resetToDefaults',
      async () => {
        try {
          console.log('[SETTINGS] Resetting settings to defaults...')
          
          // Define default settings
          const defaultSettings = {
            'theme': 'system',
            'auto-save': true,
            'notifications': true,
            'portable-mode-enabled': false,
            'db-path': '',
            'mode-switch-count': 0
          }
          
          // Apply default settings
          for (const [key, value] of Object.entries(defaultSettings)) {
            this.dbManager.setSetting(key, value)
          }
          
          console.log('[SETTINGS] Successfully reset settings to defaults')
          return { success: true, defaultsApplied: Object.keys(defaultSettings) }
        } catch (error) {
          console.error('[SETTINGS] Failed to reset settings to defaults:', error)
          throw error
        }
      },
      {
        description: 'Reset all settings to their default values'
      }
    )

    this.log('info', 'Simple Settings Module endpoints registered successfully')
  }
}
