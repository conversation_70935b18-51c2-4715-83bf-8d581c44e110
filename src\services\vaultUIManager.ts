import { VaultRegistry, ContextVault, ContextFolder, ContextVaultCard, FileTreeNode } from '../types'
import { faFolder, faFile, faFileText, faCube, faImage } from '@fortawesome/free-solid-svg-icons'
import { cacheManager } from './cacheManager'
import { generateContentId } from '../utils/idGenerator'
import { metadataRepairService } from './metadataRepairService'
import { vault, path as pathAPI, settings } from '../api/UnifiedAPIClient'

/**
 * VaultUIManager Service
 * Handles scanning vaults, loading context data, and providing UI data structure
 * ✅ V02 COMPLIANT: Uses PathResolver for all path operations per architecture guidelines
 * 🚀 OPTIMIZED: Added caching and performance improvements for faster home page loading
 */
export class VaultUIManager {
  // State management optimization properties
  private scanDebounceTimer: NodeJS.Timeout | null = null
  private pendingScanRequests: Map<string, { resolve: (value: any) => void; reject: (error: any) => void }> = new Map()
  private cacheWarmupTimer: NodeJS.Timeout | null = null
  private lastScanResult: Map<string, { result: any; timestamp: number }> = new Map()
  private isScanning = false

  /**
   * ✅ MIGRATED: Use UnifiedAPIClient path operations
   */
  private async joinPath(...parts: string[]): Promise<string> {
    if (parts.length === 0) return ''
    if (parts.length === 1) return parts[0]

    try {
      const result = await pathAPI.join(...parts)
      return result.success && result.path ? result.path : parts.join('/')
    } catch (error) {
      console.warn('[VaultUIManager] Path join failed, using fallback:', error)
      return parts.join('/')
    }
  }

  constructor() {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI?.events?.on) {
        (window as any).electronAPI.events.on('vault:rootChanged', async () => {
          try {
            await this.clearCaches()
            console.log('[VaultUIManager] Cleared caches on vault:rootChanged')
          } catch (e) {
            console.warn('[VaultUIManager] Failed clearing caches on vault:rootChanged', (e as Error)?.message)
          }
        })
      }
    } catch {}
  }

  /**
   * Generate a stable ID based on the context path
   * This ensures the same context always gets the same ID across scans
   */
  private generateStableId(contextPath: string): string {
    return generateContentId(contextPath, 'context');
  }

  /**
   * Clear all caches to ensure fresh data
   */
  async clearCaches(): Promise<void> {
    try {
      console.log('🔍 [VAULT-SETTING] === CLEAR CACHES START ===')
      
      // Clear vault registry cache specifically
      await this.clearVaultRegistryCache()
      
      // Clear other caches
      await cacheManager.remove('vault_scan_result')
      await cacheManager.remove('context_scan_result')
      
      console.log('🔍 [VAULT-SETTING] ✅ All caches cleared successfully')
      console.log('🔍 [VAULT-SETTING] === CLEAR CACHES END ===')
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error clearing caches:', error)
    }
  }
  
  /**
   * Warm up cache in background for better startup performance
   */
  async warmupCache(): Promise<void> {
    if (this.cacheWarmupTimer) {
      console.log('🔍 [VAULT-SETTING] Cache warmup already scheduled')
      return
    }
    
    console.log('🔍 [VAULT-SETTING] Scheduling cache warmup...')
    this.cacheWarmupTimer = setTimeout(async () => {
      try {
        console.log('🔍 [VAULT-SETTING] Starting background cache warmup...')
        await this.scanVaults(false) // Non-blocking background scan
        console.log('🔍 [VAULT-SETTING] ✅ Cache warmup completed')
      } catch (error) {
        console.warn('🔍 [VAULT-SETTING] Cache warmup failed:', error)
      } finally {
        this.cacheWarmupTimer = null
      }
    }, 2000) // Wait 2 seconds after initialization
    
    console.log('🔍 [VAULT-SETTING] Cache warmup scheduled for 2 seconds from now')
  }

  /**
   * Clear vault registry cache specifically
   */
  async clearVaultRegistryCache(): Promise<void> {
    try {
      console.log('🔍 [VAULT-SETTING] Clearing vault registry cache...')
      await cacheManager.remove('vault_registry')
      console.log('🔍 [VAULT-SETTING] ✅ Vault registry cache cleared')
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error clearing vault registry cache:', error)
    }
  }

  /**
   * Get vault registry from storage with caching
   */
  async getVaultRegistry(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY START ===')
      console.log('🔍 [VAULT-SETTING] Checking cache first...')
      
      // Check cache first for faster loading
      const cacheKey = 'vault_registry'
      const cachedRegistry = await cacheManager.get<VaultRegistry>(cacheKey)
      
      if (cachedRegistry) {
        console.log('🔍 [VAULT-SETTING] ✅ Cache hit - returning cached registry')
        console.log('🔍 [VAULT-SETTING] Cached registry structure:', {
          vaultRoot: cachedRegistry.vaultRoot,
          vaultsCount: cachedRegistry.vaults?.length || 0,
          contextsCount: cachedRegistry.vaults?.reduce((sum, v) => sum + (v.contexts?.length || 0), 0) || 0
        })
        
        // SECURITY: Validate cached registry paths still exist
        console.log('🔍 [VAULT-SETTING] Validating cached registry paths...')
        const isCachedRegistryValid = await this.validateCachedRegistry(cachedRegistry)
        
        if (!isCachedRegistryValid) {
          console.log('🔍 [VAULT-SETTING] ⚠️ Cached registry contains invalid paths, clearing cache...')
          await cacheManager.remove(cacheKey)
          console.log('🔍 [VAULT-SETTING] 💾 Cache cleared, will load fresh registry')
        } else {
          console.log('🔍 [VAULT-SETTING] ✅ Cached registry paths are valid')
          console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (CACHE) ===')
          return cachedRegistry
        }
      }

      console.log('🔍 [VAULT-SETTING] ❌ Cache miss or invalid - loading via UnifiedAPIClient...')

      console.log('🔍 [VAULT-SETTING] Calling vault.getVaultRegistry()...')
      const response = await vault.getVaultRegistry()
      console.log('🔍 [VAULT-SETTING] Result from UnifiedAPIClient:', response)
      
      if (response && response.success && response.data) {
        const registry = response.data
        console.log('🔍 [VAULT-SETTING] Registry loaded successfully, analyzing structure:')
        console.log('🔍 [VAULT-SETTING] - Vault root:', registry.vaultRoot)
        console.log('🔍 [VAULT-SETTING] - Vaults count:', registry.vaults?.length || 0)
        
        if (registry.vaults && Array.isArray(registry.vaults)) {
          registry.vaults.forEach((vault, index) => {
            console.log(`🔍 [VAULT-SETTING] - Vault ${index + 1}:`, {
              name: vault.name,
              path: vault.path,
              pathType: typeof vault.path,
              contextsCount: vault.contexts?.length || 0
            })
          })
          
          // Ensure all vaults have default colors
          registry.vaults = registry.vaults.map(vault => ({
            ...vault,
            color: vault.color || this.getDefaultVaultColor(vault.name),
            // Ensure contexts also have default colors
            contexts: vault.contexts?.map(context => ({
              ...context,
              color: context.color || this.getDefaultContextColor(context.path)
            })) || []
          }))
          console.log('🔍 [VAULT-SETTING] ✅ Applied default colors to vaults and contexts')
        }
        
        // Cache the registry for future use (5 minute TTL for fresh data)
        await cacheManager.set(cacheKey, registry)
        console.log('🔍 [VAULT-SETTING] 💾 Cached registry for future use')
      } else {
        console.log('🔍 [VAULT-SETTING] ⚠️ No registry returned from electronAPI')
        console.log('🔍 [VAULT-SETTING] Attempting to initialize vault system...')
        
        // Try to initialize the vault system
        const initializedRegistry = await this.initializeVaultSystem()
        if (initializedRegistry) {
          console.log('🔍 [VAULT-SETTING] ✅ Vault system initialized successfully')
          
          // Cache the initialized registry
          await cacheManager.set(cacheKey, initializedRegistry)
          console.log('🔍 [VAULT-SETTING] 💾 Cached initialized registry')
          
          console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (INITIALIZED) ===')
          return initializedRegistry
        } else {
          console.log('🔍 [VAULT-SETTING] ❌ Failed to initialize vault system')
        }
      }
      
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END ===')
      return registry
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error loading vault registry:', error)
      console.log('🔍 [VAULT-SETTING] === GET VAULT REGISTRY END (ERROR) ===')
      return null
    }
  }
  
  /**
   * Validate that cached registry paths still exist
   */
  private async validateCachedRegistry(registry: VaultRegistry): Promise<boolean> {
    try {
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY START ===')
      
      if (!registry.vaultRoot) {
        console.log('🔍 [VAULT-SETTING] ❌ Cached registry has no vault root')
        return false
      }
      
      // ✅ MIGRATED: Check if vault root exists using UnifiedAPIClient
      const rootExists = await vault.pathExists(registry.vaultRoot)
      if (!rootExists.exists) {
        console.log('🔍 [VAULT-SETTING] ❌ Cached vault root does not exist:', registry.vaultRoot)
        return false
      }
      console.log('🔍 [VAULT-SETTING] ✅ Cached vault root exists:', registry.vaultRoot)

      // ✅ MIGRATED: Check if vaults exist using UnifiedAPIClient
      if (registry.vaults && Array.isArray(registry.vaults)) {
        for (const vaultItem of registry.vaults) {
          if (vaultItem.path) {
            const vaultExists = await vault.pathExists(vaultItem.path)
            if (!vaultExists.exists) {
              console.log('🔍 [VAULT-SETTING] ❌ Cached vault path does not exist:', vaultItem.path)
              return false
            }
            console.log('🔍 [VAULT-SETTING] ✅ Cached vault path exists:', vaultItem.path)
          }
        }
      }
      
      console.log('🔍 [VAULT-SETTING] ✅ All cached registry paths are valid')
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY END ===')
      return true
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error validating cached registry:', error)
      console.log('🔍 [VAULT-SETTING] === VALIDATE CACHED REGISTRY END (ERROR) ===')
      return false
    }
  }

  /**
   * Validate and repair vault registry data to ensure all required properties exist
   */
  private validateAndRepairRegistry(registry: VaultRegistry): VaultRegistry {
    console.log('🔍 [VAULT-VALIDATION] Starting registry validation and repair...')
    
    if (!registry.vaults || !Array.isArray(registry.vaults)) {
      console.warn('🔍 [VAULT-VALIDATION] ⚠️ No vaults array found in registry')
      return registry
    }

    const repairedVaults = registry.vaults.map(vault => {
      // Ensure vault has all required properties
      const repairedVault: ContextVault = {
        id: vault.id || this.generateStableId(vault.path),
        name: vault.name || 'Unknown Vault',
        path: vault.path,
        color: vault.color || this.getDefaultVaultColor(vault.name || 'Unknown Vault'),
        icon: vault.icon || 'fa-folder',
        created: vault.created || new Date().toISOString(),
        lastAccessed: vault.lastAccessed || new Date().toISOString(),
        contexts: []
      }

      // Repair contexts if they exist
      if (vault.contexts && Array.isArray(vault.contexts)) {
        repairedVault.contexts = vault.contexts.map(context => ({
          id: context.id || this.generateStableId(context.path),
          name: context.name || 'Unknown Context',
          path: context.path,
          description: context.description || `Context for ${context.name || 'Unknown Context'}`,
          color: context.color || this.getDefaultContextColor(context.path),
          icon: context.icon || 'fa-folder',
          status: context.status || 'empty',
          stats: context.stats || {
            fileCount: 0,
            conversationCount: 0,
            lastModified: new Date().toISOString(),
            sizeBytes: 0
          },
          masterDoc: context.masterDoc || {
            exists: false,
            path: 'master.md',
            preview: '',
            wordCount: 0,
            lastUpdated: new Date().toISOString()
          },
          aiInsights: context.aiInsights || {
            suggestedActions: [],
            contextType: 'project',
            readinessScore: 0
          }
        }))
      }

      console.log(`🔍 [VAULT-VALIDATION] ✅ Repaired vault: ${repairedVault.name} (color: ${repairedVault.color})`)
      return repairedVault
    })

    console.log(`🔍 [VAULT-VALIDATION] ✅ Registry validation complete. Repaired ${repairedVaults.length} vaults`)
    
    return {
      ...registry,
      vaults: repairedVaults
    }
  }

  /**
   * Scan all vaults and return updated registry with caching and debouncing
   */
  async scanVaults(force = false): Promise<VaultRegistry | null> {
    // If scan is already in progress and not forced, queue the request
    if (this.isScanning && !force) {
      console.log('🔍 [VAULT-SETTING] Scan already in progress, queuing request...')
      return new Promise((resolve, reject) => {
        const requestId = `scan_${Date.now()}_${Math.random()}`
        this.pendingScanRequests.set(requestId, { resolve, reject })
        
        // Auto-cleanup after 30 seconds
        setTimeout(() => {
          this.pendingScanRequests.delete(requestId)
          reject(new Error('Scan request timed out'))
        }, 30000)
      })
    }
    
    // Debounce rapid scan requests (unless forced)
    if (this.scanDebounceTimer && !force) {
      console.log('🔍 [VAULT-SETTING] Debouncing rapid scan request...')
      clearTimeout(this.scanDebounceTimer)
      
      return new Promise((resolve, reject) => {
        this.scanDebounceTimer = setTimeout(async () => {
          try {
            const result = await this.performVaultScan()
            resolve(result)
          } catch (error) {
            reject(error)
          }
        }, 100) // 100ms debounce
      })
    }
    
    // Perform the actual scan
    return this.performVaultScan()
  }

  /**
   * Internal method to perform the actual vault scanning
   */
  private async performVaultScan(): Promise<VaultRegistry | null> {
    try {
      this.isScanning = true
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS START ===')
      
      // Get current registry (this will auto-initialize if needed)
      const registry = await this.getVaultRegistry()
      if (!registry) {
        console.log('🔍 [VAULT-SETTING] ⚠️ No registry found and initialization failed, cannot scan vaults')
        console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END (NO REGISTRY) ===')
        return null
      }

      // Validate and repair registry data to ensure all required properties exist
      const validatedRegistry = this.validateAndRepairRegistry(registry)
      console.log('🔍 [VAULT-SETTING] Registry validated and repaired, starting vault scan process')
      console.log('🔍 [VAULT-SETTING] Total vaults to scan:', validatedRegistry.vaults?.length || 0)
      
      // Discover new vaults that might not be in the registry
      console.log('🔍 [VAULT-SETTING] Discovering new vaults...')
      const newVaults = await this.discoverNewVaults(validatedRegistry.vaultRoot)
      console.log('🔍 [VAULT-SETTING] New vaults discovered:', newVaults.length)
      
      // Merge new vaults with existing ones
      const allVaults = [...(validatedRegistry.vaults || []), ...newVaults]
      console.log('🔍 [VAULT-SETTING] Total vaults to scan (including new):', allVaults.length)
      
      // Clear caches to ensure fresh data
      await this.clearCaches()
      console.log('🔍 [VAULT-SETTING] Caches cleared for fresh scan')

      // Scan each vault (including newly discovered ones)
      const updatedVaults: ContextVault[] = []
      if (allVaults && Array.isArray(allVaults)) {
        for (let i = 0; i < allVaults.length; i++) {
          const vault = allVaults[i]
          console.log(`🔍 [VAULT-SETTING] --- Scanning Vault ${i + 1}/${allVaults.length} ---`)
          console.log(`🔍 [VAULT-SETTING] Vault details:`, {
            name: vault.name,
            path: vault.path,
            pathType: typeof vault.path,
            contextsCount: vault.contexts?.length || 0,
            isNew: newVaults.some(nv => nv.path === vault.path)
          })
          
          const updatedVault = await this.scanVault(vault)
          if (updatedVault) {
            updatedVaults.push(updatedVault)
            console.log(`🔍 [VAULT-SETTING] ✅ Vault ${vault.name} scanned successfully`)
          } else {
            console.log(`🔍 [VAULT-SETTING] ❌ Vault ${vault.name} scan failed or returned null`)
          }
        }
      }

      console.log('🔍 [VAULT-SETTING] Vault scanning complete')
      console.log('🔍 [VAULT-SETTING] Successfully scanned vaults:', updatedVaults.length)
      console.log('🔍 [VAULT-SETTING] Failed vaults:', (validatedRegistry.vaults?.length || 0) - updatedVaults.length)

      // Create updated registry
      const updatedRegistry: VaultRegistry = {
        ...validatedRegistry,
        vaults: updatedVaults,
        lastScan: new Date().toISOString()
      }

      console.log('🔍 [VAULT-SETTING] Updated registry created with scan timestamp')
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END ===')
      
      // Resolve all pending scan requests
      this.pendingScanRequests.forEach(({ resolve }) => {
        try {
          resolve(updatedRegistry)
        } catch (error) {
          console.warn('🔍 [VAULT-SETTING] Error resolving pending scan request:', error)
        }
      })
      this.pendingScanRequests.clear()
      
      return updatedRegistry
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error scanning vaults:', error)
      console.log('🔍 [VAULT-SETTING] === SCAN VAULTS END (ERROR) ===')
      
      // Reject all pending scan requests
      this.pendingScanRequests.forEach(({ reject }) => {
        try {
          reject(error)
        } catch (rejectError) {
          console.warn('🔍 [VAULT-SETTING] Error rejecting pending scan request:', rejectError)
        }
      })
      this.pendingScanRequests.clear()
      
      return null
    } finally {
      this.isScanning = false
    }
  }

  /**
   * Discover new vaults in the vault root directory
   */
  private async discoverNewVaults(vaultRoot: string): Promise<ContextVault[]> {
    try {
      console.log('🔍 [VAULT-SETTING] === DISCOVER NEW VAULTS START ===')
      console.log('🔍 [VAULT-SETTING] Scanning vault root for new vaults:', vaultRoot)
      console.log('🔍 [VAULT-SETTING] Vault root type:', typeof vaultRoot)
      console.log('🔍 [VAULT-SETTING] Vault root length:', vaultRoot?.length)

      // Validate vault root path before proceeding
      if (!vaultRoot || typeof vaultRoot !== 'string' || vaultRoot.trim().length === 0) {
        console.error('🔍 [VAULT-SETTING] ❌ Invalid vault root path:', vaultRoot)
        return []
      }

      // ✅ MIGRATED: Read the vault root directory using UnifiedAPIClient
      const dirResult = await vault.readDirectory(vaultRoot)
      if (!dirResult.success) {
        console.error('🔍 [VAULT-SETTING] ❌ Failed to read vault root directory:', dirResult.error)
        return []
      }

      // Find potential vault directories (exclude system folders and files)
      const potentialVaults = dirResult.items.filter(item => 
        item.isDirectory && 
        !item.name.startsWith('.') && 
        !item.name.startsWith('_') &&
        !item.name.includes('ChatLo') // Exclude system folders
      )

      console.log('🔍 [VAULT-SETTING] Found potential vault directories:', potentialVaults.map(v => v.name))
      
      const newVaults: ContextVault[] = []
      
      for (const item of potentialVaults) {
        try {
          const vaultPath = await this.joinPath(vaultRoot, item.name)
          console.log('🔍 [VAULT-SETTING] Checking potential vault:', vaultPath)
          
          // ✅ MIGRATED: Check if this directory contains vault-like structure using UnifiedAPIClient
          const vaultDirResult = await vault.readDirectory(vaultPath)
          if (vaultDirResult.success) {
            // Look for context folders (personal-vault, work-vault, etc.)
            const hasContextFolders = vaultDirResult.items.some(subItem => 
              subItem.isDirectory && 
              (subItem.name.includes('vault') || subItem.name.includes('context'))
            )
            
            if (hasContextFolders) {
              console.log('🔍 [VAULT-SETTING] ✅ Found new vault with context structure:', item.name)
              
              const newVault: ContextVault = {
                id: this.generateStableId(vaultPath),
                name: item.name,
                path: vaultPath,
                color: this.getDefaultVaultColor(item.name), // Add default color
                icon: 'fa-folder', // Add default icon
                created: new Date().toISOString(), // Add created timestamp
                lastAccessed: new Date().toISOString(),
                contexts: []
              }
              
              newVaults.push(newVault)
            }
          }
        } catch (error) {
          console.warn('🔍 [VAULT-SETTING] ⚠️ Error checking potential vault:', item.name, error)
        }
      }
      
      console.log('🔍 [VAULT-SETTING] Discovered new vaults:', newVaults.length)
      console.log('🔍 [VAULT-SETTING] === DISCOVER NEW VAULTS END ===')
      
      return newVaults
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error discovering new vaults:', error)
      return []
    }
  }

  /**
   * Scan a vault for updated data
   */
  private async scanVault(vaultItem: ContextVault): Promise<ContextVault | null> {
    try {
      console.log(`🔍 [VAULT-SETTING] === SCAN VAULT START ===`)
      console.log(`🔍 [VAULT-SETTING] Scanning vault: ${vaultItem.name}`)
      console.log(`🔍 [VAULT-SETTING] Vault path: ${vaultItem.path}`)
      console.log(`🔍 [VAULT-SETTING] Vault color: ${vaultItem.color || 'undefined (will use default)'}`)

      // Ensure vault has a default color if none is set
      const vaultColor = vaultItem.color || this.getDefaultVaultColor(vaultItem.name)
      console.log(`🔍 [VAULT-SETTING] Using vault color: ${vaultColor}`)

      // ✅ MIGRATED: Check if vault path exists using UnifiedAPIClient
      const pathCheck = await vault.pathExists(vaultItem.path)
      console.log(`🔍 [VAULT-SETTING] PathExists result:`, pathCheck)
      
      if (!pathCheck.exists) {
        console.warn(`🚨 [VAULT-SETTING] Vault path does not exist: ${vaultItem.path}`)
        console.warn(`🚨 [VAULT-SETTING] PathExists error: ${pathCheck.error || 'No error message'}`)
        console.warn(`🚨 [VAULT-SETTING] This suggests a path resolution or security validation issue`)
        return null
      }

      console.log(`✅ [VAULT-SETTING] Vault path exists, proceeding with directory scan`)
      
      // PROACTIVE METADATA REPAIR: Fix corrupted metadata before scanning to prevent cascade failures
      try {
        const { metadataRepairService } = await import('./metadataRepairService')
        console.log(`🔧 [VAULT-SETTING] Starting proactive metadata repair for vault: ${vault.name}`)
        
        const repairResult = await metadataRepairService.repairVaultMetadataProactively(vault.path)
        console.log(`🔧 [VAULT-SETTING] Metadata repair complete: ${repairResult.repairedFiles} repaired, ${repairResult.failedFiles} failed`)
        
        if (repairResult.failedFiles > 0) {
          console.warn(`⚠️ [VAULT-SETTING] Some metadata files could not be repaired:`, repairResult.errors)
        }
      } catch (repairError) {
        console.warn(`⚠️ [VAULT-SETTING] Metadata repair service unavailable, continuing with scan:`, repairError)
      }
      
      // ✅ MIGRATED: Scan vault directory for contexts using UnifiedAPIClient
      const dirResult = await vault.readDirectory(vaultItem.path)
      if (!dirResult.success) {
        console.error(`❌ [VAULT-SETTING] Failed to read vault directory: ${dirResult.error}`)
        return vaultItem
      }

      // Find context folders (exclude system folders)
      const contextFolders = dirResult.items.filter(item => 
        item.isDirectory && !item.name.startsWith('.') && !item.name.startsWith('_')
      )
      
      console.log(`🔍 [VAULT-SETTING] Found ${contextFolders.length} context folders:`, 
        contextFolders.map(f => ({ name: f.name, path: f.path })))

      // Scan each context folder
      const updatedContexts: ContextFolder[] = []
      for (const folder of contextFolders) {
        const context = await this.scanContext(folder.path, vaultColor)
        if (context) {
          updatedContexts.push(context)
        }
      }

      console.log(`✅ [VAULT-SETTING] Successfully scanned ${updatedContexts.length} contexts`)
      console.log(`🔍 [VAULT-SETTING] === SCAN VAULT END ===`)

      return {
        ...vaultItem,
        color: vaultColor, // Ensure the vault has the default color
        contexts: updatedContexts,
        lastAccessed: new Date().toISOString()
      }
    } catch (error) {
      console.error(`💥 [VAULT-SETTING] Error scanning vault ${vaultItem.name}:`, error)
      return vaultItem
    }
  }

  /**
   * Scan a context folder for updated data
   */
  private async scanContext(contextPath: string, defaultColor: string): Promise<ContextFolder | null> {
    try {
      // Ensure we have a valid default color
      const fallbackColor = defaultColor || this.getDefaultContextColor(contextPath)
      console.log(`🔍 [VAULT-SCAN] Using fallback color: ${fallbackColor} for context: ${contextPath}`)
      
      // ✅ MIGRATED: Read context metadata using UnifiedAPIClient
      const metadataPath = await this.joinPath(contextPath, '.intelligence', 'metadata.json')
      const metadataResult = await vault.readFile(metadataPath)
      
      let metadata: any = {}
      let metadataValid = false
      
      if (metadataResult.success && metadataResult.content) {
        try {
          metadata = JSON.parse(metadataResult.content)
          metadataValid = true
          console.log(`✅ [VAULT-SCAN] Valid metadata loaded from ${metadataPath}`)
        } catch (e) {
          console.warn(`❌ [VAULT-SCAN] Invalid metadata JSON in ${metadataPath}:`, e)
          console.warn(`📄 [VAULT-SCAN] Content preview:`, metadataResult.content.substring(0, 200))
          console.warn(`🔧 [VAULT-SCAN] Attempting to repair corrupted metadata...`)

          // Attempt to repair the corrupted metadata
          try {
            const { metadataRepairService } = await import('./metadataRepairService')
            const repairedContent = await metadataRepairService.repairMetadataFile(metadataPath, metadataResult.content)

            if (repairedContent) {
              // ✅ MIGRATED: Write the repaired content back to the file using UnifiedAPIClient
              const writeResult = await vault.writeFile(metadataPath, repairedContent)

              if (writeResult.success) {
                try {
                  metadata = JSON.parse(repairedContent)
                  metadataValid = true
                  console.log(`✅ [VAULT-SCAN] Successfully repaired and reloaded metadata: ${metadataPath}`)
                } catch (parseError) {
                  console.error(`❌ [VAULT-SCAN] Repaired content still invalid JSON: ${metadataPath}`, parseError)
                }
              } else {
                console.error(`❌ [VAULT-SCAN] Failed to write repaired metadata: ${metadataPath}`)
              }
            }
          } catch (repairError) {
            console.error(`❌ [VAULT-SCAN] Failed to repair metadata: ${metadataPath}`, repairError)
            // Continue with empty metadata - the context will still be scanned
          }
        }
      } else {
        console.log(`ℹ️ [VAULT-SCAN] No metadata file found at ${metadataPath}`)
      }

      // ✅ MIGRATED: Scan files in context using UnifiedAPIClient
      const dirResult = await vault.readDirectory(contextPath)
      if (!dirResult.success) {
        return null
      }

      // Count files (exclude system folders)
      const files = dirResult.items.filter(item =>
        !item.name.startsWith('.') && !item.name.startsWith('_')
      )
      const fileCount = files.length

      // ✅ MIGRATED: Check for master.md using UnifiedAPIClient
      const masterPath = await this.joinPath(contextPath, 'master.md')
      const masterExists = await vault.pathExists(masterPath)
      
      let masterDoc = {
        exists: false,
        path: 'master.md',
        preview: '',
        wordCount: 0,
        lastUpdated: new Date().toISOString()
      }

      if (masterExists.exists) {
        // ✅ MIGRATED: Read master file using UnifiedAPIClient
        const masterResult = await vault.readFile(masterPath)
        if (masterResult.success && masterResult.content) {
          const content = masterResult.content
          masterDoc = {
            exists: true,
            path: 'master.md',
            preview: content.substring(0, 100).replace(/\n/g, ' ') + (content.length > 100 ? '...' : ''),
            wordCount: content.split(/\s+/).length,
            lastUpdated: new Date().toISOString()
          }
        }
      }

      // Determine context status
      let status: 'empty' | 'active' | 'growing' | 'archived' = 'empty'
      if (fileCount > 10) status = 'growing'
      else if (fileCount > 1) status = 'active'

      // Calculate total size
      let totalSize = 0
      for (const file of files) {
        if (file.size) totalSize += file.size
      }

      const contextName = metadata.name || contextPath.split(/[\/\\]/).pop() || 'Unknown Context'
      
      // Ensure we always have a valid color, even if metadata repair failed
      let finalColor = fallbackColor
      if (metadataValid && metadata.color && typeof metadata.color === 'string') {
        finalColor = metadata.color
      } else {
        console.log(`🔍 [VAULT-SCAN] Using fallback color for ${contextName}: ${finalColor}`)
      }
      
      console.log(`🔍 [VAULT-SCAN] Final color for ${contextName}: ${finalColor}`)
      
      return {
        id: metadata.id || this.generateStableId(contextPath),
        name: contextName,
        path: contextPath,
        description: metadata.description || `Context vault for ${contextName}`,
        color: finalColor,
        icon: metadata.icon || 'fa-folder',
        status,
        stats: {
          fileCount,
          conversationCount: 0, // TODO: Count linked conversations
          lastModified: new Date().toISOString(),
          sizeBytes: totalSize
        },
        masterDoc,
        aiInsights: {
          suggestedActions: this.generateSuggestions(status, fileCount),
          contextType: metadata.contextType || 'project',
          readinessScore: Math.min(fileCount / 10, 1)
        }
      }
    } catch (error) {
      console.error(`Error scanning context ${contextPath}:`, error)
      return null
    }
  }

  /**
   * Get vault cards with optimized loading
   */
  async getVaultCards(): Promise<ContextVaultCard[]> {
    try {
      // Check cache first for instant loading
      const cardsCacheKey = 'vault_cards'
      const cachedCards = await cacheManager.get<ContextVaultCard[]>(cardsCacheKey)
      
      if (cachedCards) {
        console.log('getVaultCards: ✅ Cache hit - returning cached cards')
        return cachedCards
      }

      console.log('getVaultCards: ❌ Cache miss - scanning vaults...')
      const registry = await this.scanVaults()
      if (!registry) return []

      // Ensure vaults is an array
      if (!registry.vaults || !Array.isArray(registry.vaults)) {
        console.warn('Registry vaults is not an array in getVaultCards:', registry.vaults)
        return []
      }

      const cards: ContextVaultCard[] = []

      for (const vault of registry.vaults) {
        // Ensure contexts is an array
        if (!vault.contexts || !Array.isArray(vault.contexts)) {
          console.warn('Vault contexts is not an array:', vault.contexts)
          continue
        }

        for (const context of vault.contexts) {
          cards.push(this.contextToCard(context))
        }
      }

      // Cache the cards for future use
      await cacheManager.set(cardsCacheKey, cards)
      console.log('getVaultCards: 💾 Cached cards for future use')

      return cards
    } catch (error) {
      console.error('Error getting vault cards:', error)
      return []
    }
  }

  /**
   * Get vault cards with background refresh (for progressive loading)
   */
  async getVaultCardsWithBackgroundRefresh(): Promise<{
    cards: ContextVaultCard[]
    isFromCache: boolean
    backgroundRefreshPromise?: Promise<void>
  }> {
    try {
      // Try to get cached cards first for instant display
      const cardsCacheKey = 'vault_cards'
      const cachedCards = await cacheManager.get<ContextVaultCard[]>(cardsCacheKey)
      
      if (cachedCards) {
        console.log('getVaultCardsWithBackgroundRefresh: ✅ Cache hit - showing cached cards immediately')
        
        // Start background refresh in parallel
        const backgroundRefreshPromise = this.refreshVaultCardsInBackground()
        
        return {
          cards: cachedCards,
          isFromCache: true,
          backgroundRefreshPromise
        }
      }

      // No cache, do full scan
      console.log('getVaultCardsWithBackgroundRefresh: ❌ No cache - doing full scan...')
      const cards = await this.getVaultCards()
      
      return {
        cards,
        isFromCache: false
      }
    } catch (error) {
      console.error('Error in getVaultCardsWithBackgroundRefresh:', error)
      return {
        cards: [],
        isFromCache: false
      }
    }
  }

  /**
   * Refresh vault cards in background without blocking UI
   */
  private async refreshVaultCardsInBackground(): Promise<void> {
    try {
      console.log('refreshVaultCardsInBackground: 🔄 Starting background refresh...')
      
      // Use setTimeout to defer the heavy operation
      setTimeout(async () => {
        try {
          await this.getVaultCards() // This will update the cache
          console.log('refreshVaultCardsInBackground: ✅ Background refresh completed')
        } catch (error) {
          console.warn('refreshVaultCardsInBackground: ⚠️ Background refresh failed:', error)
        }
      }, 100) // Small delay to ensure UI is responsive
      
    } catch (error) {
      console.error('Error starting background refresh:', error)
    }
  }

  /**
   * Convert context folders to vault cards for UI (one card per context)
   */
  private contextToCard(context: ContextFolder): ContextVaultCard {
    return {
      id: context.id,
      name: context.name,
      description: context.description,
      color: context.color,
      icon: context.icon,
      status: context.status,
      fileCount: context.stats.fileCount,
      conversationCount: context.stats.conversationCount,
      lastActivity: this.formatLastActivity(context.stats.lastModified),
      masterPreview: context.masterDoc.preview,
      recentFiles: [], // TODO: Get recent files
      suggestedActions: context.aiInsights.suggestedActions,
      contextType: context.aiInsights.contextType,
      readinessScore: context.aiInsights.readinessScore,
      quickActions: this.getQuickActions(context.status, context.stats.fileCount),
      path: context.path
    }
  }

  /**
   * Convert vault to vault card (one card per vault, files go to /vault/documents)
   */
  private vaultToCard(vault: ContextVault): ContextVaultCard {
    // Aggregate stats from all contexts in the vault
    let totalFileCount = 0
    let totalConversationCount = 0
    let lastModified = vault.created

    if (vault.contexts && Array.isArray(vault.contexts)) {
      for (const context of vault.contexts) {
        totalFileCount += context.stats.fileCount
        totalConversationCount += context.stats.conversationCount
        if (context.stats.lastModified > lastModified) {
          lastModified = context.stats.lastModified
        }
      }
    }

    // Use vault properties, with documents folder as the target
    return {
      id: vault.id,
      name: vault.name,
      description: `Drop files to ${vault.name}/documents`,
      path: vault.path,
      color: vault.color,
      icon: vault.icon,
      status: totalFileCount > 0 ? 'active' : 'empty',
      fileCount: totalFileCount,
      conversationCount: totalConversationCount,
      lastActivity: this.formatLastActivity(lastModified),
      masterPreview: `Files will be saved to ${vault.name}/documents folder`,
      recentFiles: [], // TODO: Get recent files from documents folder
      suggestedActions: ['Drop files here', 'Start a conversation', 'Organize documents'],
      contextType: 'vault',
      readinessScore: Math.min(totalFileCount / 10, 1),
      quickActions: {
        primary: { label: 'Drop Files', action: 'drop' },
        secondary: { label: 'Browse', action: 'browse' }
      }
    }
  }

  /**
   * Get file tree for Files page
   */
  async getFileTree(contextId?: string): Promise<FileTreeNode[]> {
    try {
      console.log('getFileTree: Loading vault registry...')
      const registry = await this.getVaultRegistry()
      console.log('getFileTree: Registry loaded:', registry)
      if (!registry) {
        console.log('getFileTree: No registry found, returning empty array')
        return []
      }

      // If contextId specified, return tree for that context
      if (contextId) {
        return await this.getContextFileTree(contextId, registry)
      }

      // Return tree for all vaults
      const tree: FileTreeNode[] = []

      // Add shared dropbox as first item
      const sharedDropboxNode = await this.buildSharedDropboxTree(registry.vaultRoot)
      if (sharedDropboxNode) {
        tree.push(sharedDropboxNode)
      }

      // Ensure vaults is an array
      if (!registry.vaults || !Array.isArray(registry.vaults)) {
        console.warn('Registry vaults is not an array in getFileTree:', registry.vaults)
        return tree // Return tree with shared dropbox even if no vaults
      }

      for (const vault of registry.vaults) {
        const vaultNode: FileTreeNode = {
          type: 'folder',
          name: vault.name,
          path: vault.path,
          icon: faFolder,
          color: vault.color,
          children: []
        }

        // Ensure contexts is an array
        if (vault.contexts && Array.isArray(vault.contexts)) {
          for (const context of vault.contexts) {
            const contextNode = await this.buildContextTree(context)
            if (contextNode) {
              vaultNode.children!.push(contextNode)
            }
          }
        }

        tree.push(vaultNode)
      }

      return tree
    } catch (error) {
      console.error('Error getting file tree:', error)
      return []
    }
  }

  /**
   * Build shared dropbox tree
   */
  private async buildSharedDropboxTree(vaultRoot: string): Promise<FileTreeNode | null> {
    try {
      const sharedPath = `${vaultRoot}/shared-dropbox`

      // ✅ MIGRATED: Check if shared dropbox exists using UnifiedAPIClient
      const pathCheck = await vault.pathExists(sharedPath)
      if (!pathCheck.exists) {
        console.log('Shared dropbox does not exist at:', sharedPath)
        return null
      }

      // ✅ MIGRATED: Read shared dropbox directory using UnifiedAPIClient
      const dirResult = await vault.readDirectory(sharedPath)
      if (!dirResult.success) {
        console.error('Failed to read shared dropbox directory:', dirResult.error)
        return null
      }

      const sharedNode: FileTreeNode = {
        type: 'folder',
        name: '📦 Shared Dropbox',
        path: sharedPath,
        icon: faFolder,
        color: '#FFA500', // Orange color for shared dropbox
        children: []
      }

      // Add files from shared dropbox (exclude metadata files)
      for (const item of dirResult.items) {
        if (item.name.startsWith('.')) continue // Skip metadata files

        const fileNode: FileTreeNode = {
          type: item.isDirectory ? 'folder' : 'file',
          name: item.name,
          path: item.path,
          icon: item.isDirectory ? faFolder : faFile,
          size: item.size,
          modified: item.modified
        }

        // ✅ MIGRATED: If it's a directory, recursively build its tree using UnifiedAPIClient
        if (item.isDirectory) {
          const subDirResult = await vault.readDirectory(item.path)
          if (subDirResult.success) {
            fileNode.children = []
            for (const subItem of subDirResult.items) {
              fileNode.children.push({
                type: subItem.isDirectory ? 'folder' : 'file',
                name: subItem.name,
                path: subItem.path,
                icon: subItem.isDirectory ? faFolder : faFile,
                size: subItem.size,
                modified: subItem.modified
              })
            }
          }
        }

        sharedNode.children!.push(fileNode)
      }

      return sharedNode
    } catch (error) {
      console.error('Error building shared dropbox tree:', error)
      return null
    }
  }

  /**
   * Build file tree for a context
   */
  private async buildContextTree(context: ContextFolder): Promise<FileTreeNode | null> {
    try {
      // ✅ MIGRATED: Read context directory using UnifiedAPIClient
      const dirResult = await vault.readDirectory(context.path)
      if (!dirResult.success) return null

      const contextNode: FileTreeNode = {
        type: 'folder',
        name: context.name,
        path: context.path,
        icon: faFolder,
        color: context.color,
        children: []
      }

      // Add master.md first if it exists
      if (context.masterDoc.exists) {
        contextNode.children!.push({
          type: 'file',
          name: 'master.md',
          path: await this.joinPath(context.path, 'master.md'),
          icon: faFileText,
          color: 'text-primary'
        })
      }

      // Add other files and folders
      for (const item of dirResult.items) {
        if (item.name.startsWith('.') || item.name === 'master.md') continue

        const node: FileTreeNode = {
          type: item.isDirectory ? 'folder' : 'file',
          name: item.name,
          path: item.path,
          icon: this.getFileIcon(item.name, item.isDirectory),
          color: this.getFileColor(item.name, item.isDirectory),
          size: item.size,
          modified: item.modified
        }

        if (item.isDirectory) {
          // TODO: Recursively build folder contents if needed
          node.children = []
        }

        contextNode.children!.push(node)
      }

      return contextNode
    } catch (error) {
      console.error(`Error building context tree for ${context.name}:`, error)
      return null
    }
  }

  /**
   * Get context file tree for specific context
   */
  async getContextFileTree(contextId: string, registry?: VaultRegistry): Promise<FileTreeNode[]> {
    // Load registry if not provided
    if (!registry) {
      registry = await this.getVaultRegistry() || undefined
      if (!registry) return []
    }

    // Handle shared dropbox case (empty contextId)
    if (!contextId || contextId === '') {
      const sharedDropboxNode = await this.buildSharedDropboxTree(registry.vaultRoot)
      return sharedDropboxNode ? [sharedDropboxNode] : []
    }

    // Find the context
    let targetContext: ContextFolder | null = null

    // Ensure vaults is an array
    if (registry.vaults && Array.isArray(registry.vaults)) {
      for (const vault of registry.vaults) {
        // Ensure contexts is an array
        if (vault.contexts && Array.isArray(vault.contexts)) {
          const context = vault.contexts.find(c => c.id === contextId)
          if (context) {
            targetContext = context
            break
          }
        }
      }
    }

    if (!targetContext) return []

    const contextTree = await this.buildContextTree(targetContext)
    return contextTree ? [contextTree] : []
  }

  /**
   * Generate suggestions based on context state
   */
  private generateSuggestions(status: string, _fileCount: number): string[] {
    if (status === 'empty') {
      return [
        '📁 Drop files here to get started',
        '💬 Start a conversation about your goals',
        '🎯 Tell me what you\'re working on'
      ]
    } else if (status === 'active') {
      return [
        '📋 Organize your files into folders',
        '🤖 Ask AI to analyze your content',
        '📝 Update your master document'
      ]
    } else {
      return [
        '🔍 Search across all your files',
        '📊 Generate project insights',
        '🎯 Plan your next steps'
      ]
    }
  }

  /**
   * Get quick actions based on context state
   */
  private getQuickActions(status: string, _fileCount: number): { primary: { label: string; action: string }; secondary: { label: string; action: string } } {
    if (status === 'empty') {
      return {
        primary: { label: 'Add Files', action: 'open-file-picker' },
        secondary: { label: 'Start Chat', action: 'open-chat-with-context' }
      }
    } else {
      return {
        primary: { label: 'Browse Files', action: 'open-files-page' },
        secondary: { label: 'Continue Chat', action: 'open-chat-with-context' }
      }
    }
  }

  /**
   * Format last activity timestamp
   */
  private formatLastActivity(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  /**
   * Get appropriate icon for file/folder
   */
  private getFileIcon(name: string, isDirectory: boolean) {
    if (isDirectory) {
      if (name === 'documents') return faFileText
      if (name === 'images') return faImage
      if (name === 'artifacts') return faCube
      return faFolder
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
      case 'txt':
        return faFileText
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return faImage
      default:
        return faFile
    }
  }

  /**
   * Get appropriate color for file/folder
   */
  private getFileColor(name: string, isDirectory: boolean): string {
    if (isDirectory) {
      if (name === 'documents') return 'text-blue-400'
      if (name === 'images') return 'text-green-400'
      if (name === 'artifacts') return 'text-purple-400'
      return 'text-supplement1'
    }

    const ext = name.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
        return 'text-primary'
      case 'txt':
        return 'text-supplement1'
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'text-green-400'
      default:
        return 'text-supplement1'
    }
  }

  /**
   * Initialize vault system if no registry exists
   */
  async initializeVaultSystem(): Promise<VaultRegistry | null> {
    try {
      console.log('🔍 [VAULT-SETTING] === INITIALIZE VAULT SYSTEM START ===')
      
      // Check if we already have a registry
      const existingRegistry = await this.getVaultRegistry()
      if (existingRegistry) {
        console.log('🔍 [VAULT-SETTING] ✅ Vault registry already exists, no initialization needed')
        return existingRegistry
      }
      
      console.log('🔍 [VAULT-SETTING] ⚠️ No vault registry found, initializing new vault system...')

      // Get the default vault root path
      const defaultVaultRoot = await this.getDefaultVaultRoot()
      console.log('🔍 [VAULT-SETTING] Default vault root:', defaultVaultRoot)

      // ✅ MIGRATED: Initialize vault root with default template using UnifiedAPIClient
      const initResult = await vault.initializeVaultRoot(defaultVaultRoot, 'default')

      // Handle both APIResponse envelope and direct response for backward compatibility
      const result = initResult.data || initResult
      if (!result.success) {
        console.error('🔍 [VAULT-SETTING] ❌ Failed to initialize vault root:', result.error)
        return null
      }
      
      console.log('🔍 [VAULT-SETTING] ✅ Vault root initialized successfully')
      console.log('🔍 [VAULT-SETTING] Created vaults:', result.vaults?.length || 0)

      // Create a basic registry structure
      const newRegistry: VaultRegistry = {
        version: '1.0',
        vaultRoot: defaultVaultRoot,
        vaults: result.vaults || [],
        lastScan: new Date().toISOString(),
        preferences: {}
      }
      
      // ✅ MIGRATED: Save the new registry using UnifiedAPIClient
      const saveResult = await vault.saveVaultRegistry(newRegistry)
      if (saveResult.success) {
        console.log('🔍 [VAULT-SETTING] ✅ New vault registry saved successfully')
      } else {
        console.warn('🔍 [VAULT-SETTING] ⚠️ Failed to save vault registry:', saveResult.error)
      }
      
      console.log('🔍 [VAULT-SETTING] === INITIALIZE VAULT SYSTEM END ===')
      return newRegistry
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] 💥 Error initializing vault system:', error)
      return null
    }
  }
  
  /**
   * Repair all corrupted metadata files in all vaults
   */
  async repairAllVaultMetadata(): Promise<{ totalRepaired: number, vaultResults: Array<{ vaultName: string, repaired: number }> }> {
    console.log('🔧 [VAULT-REPAIR] Starting bulk metadata repair...')

    const results = {
      totalRepaired: 0,
      vaultResults: [] as Array<{ vaultName: string, repaired: number }>
    }

    try {
      const registry = await this.getVaultRegistry()

      for (const vault of registry.vaults) {
        console.log(`🔧 [VAULT-REPAIR] Repairing metadata in vault: ${vault.name}`)

        const repairedCount = await metadataRepairService.repairVaultMetadata(vault.path)

        results.vaultResults.push({
          vaultName: vault.name,
          repaired: repairedCount
        })

        results.totalRepaired += repairedCount

        console.log(`✅ [VAULT-REPAIR] Repaired ${repairedCount} metadata files in vault: ${vault.name}`)
      }

      console.log(`🎉 [VAULT-REPAIR] Bulk repair completed. Total files repaired: ${results.totalRepaired}`)

      // Clear cache to force refresh with repaired data
      await cacheManager.delete('vault_cards')

    } catch (error) {
      console.error('❌ [VAULT-REPAIR] Error during bulk metadata repair:', error)
    }

    return results
  }

  /**
   * Get default vault root path
   */
  private async getDefaultVaultRoot(): Promise<string> {
    try {
      // ✅ MIGRATED: Try to get from settings first using UnifiedAPIClient
      const savedRoot = await settings.get('vault-root-path')
      if (savedRoot) {
        console.log('🔍 [VAULT-SETTING] Using saved vault root from settings:', savedRoot)
        return savedRoot
      }
      
      // Fallback to default path
      const defaultPath = 'C:\\Users\\<USER>\\Documents\\ChatLo_Vaults'
      console.log('🔍 [VAULT-SETTING] Using default vault root path:', defaultPath)
      return defaultPath
      
    } catch (error) {
      console.error('🔍 [VAULT-SETTING] Error getting default vault root:', error)
      // Last resort fallback
      return 'C:\\Users\\<USER>\\Documents\\ChatLo_Vaults'
    }
  }

  /**
   * Get default color for a vault based on its name
   */
  private getDefaultVaultColor(vaultName: string): string {
    const colorMap: { [key: string]: string } = {
      'work': 'primary',
      'personal': 'secondary', 
      'project': 'supplement2',
      'study': 'supplement1',
      'archive': 'tertiary'
    }
    
    // Try to match by name first
    const lowerName = vaultName.toLowerCase()
    for (const [key, color] of Object.entries(colorMap)) {
      if (lowerName.includes(key)) {
        return color
      }
    }
    
    // Fallback to a rotating color scheme based on name hash
    const hash = vaultName.split('').reduce((a, b) => {
      a = ((a << 5) - a + b.charCodeAt(0)) & 0xffffffff
      return a
    }, 0)
    
    const colors = ['primary', 'secondary', 'supplement1', 'supplement2', 'tertiary']
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * Get default color for a context based on its path
   */
  private getDefaultContextColor(contextPath: string): string {
    const hash = contextPath.split('').reduce((a, b) => {
      a = ((a << 5) - a + b.charCodeAt(0)) & 0xffffffff
      return a
    }, 0)

    const colors = ['primary', 'secondary', 'supplement1', 'supplement2', 'tertiary']
    return colors[Math.abs(hash) % colors.length]
  }
}

export const vaultUIManager = new VaultUIManager()

// Expose repair function to global scope for debugging
if (typeof window !== 'undefined') {
  (window as any).repairVaultMetadata = async () => {
    console.log('🔧 Starting vault metadata repair from console...')
    const results = await vaultUIManager.repairAllVaultMetadata()
    console.log('🎉 Repair completed:', results)
    return results
  }
}
