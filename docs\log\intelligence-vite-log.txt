[1]     at async App.init (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\main.js:631:9)
[1] [MODULE:system] Simple System Module initialized successfully (with mock performance monitor)
[1] [MODULE:system] Registering simple system endpoints...
[1] [MODULE] Registered endpoint: system:getAPIRegistry
[1] [MODULE] Registered endpoint: system:getPerformanceMetrics
[1] [MODULE] Registered endpoint: system:cleanupMiddleware
[1] [MODULE] Registered endpoint: system:getMonitoringData
[1] [MODULE] Registered endpoint: system:getEndpointMetrics
[1] [MODULE] Registered endpoint: system:resetMonitoring
[1] [MODULE] Registered endpoint: system:getErrorStatistics
[1] [MODULE] Registered endpoint: system:clearErrorHistory
[1] [MODULE:system] Registered 8 simple system endpoints
[1] [VAULT-MODULE] ❌ Failed to initialize SimpleVaultModule: Error: Failed to resolve required dependency: database
[1]     at SimpleVaultModule.resolveDependencies (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\core\BaseAPIModule.js:102:27)
[1]     at SimpleVaultModule.initialize (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\core\BaseAPIModule.js:23:24)
[1]     at App.initializeVaultModule (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\main.js:707:37)
[1]     at async Promise.all (index 3)
[1]     at async App.initializeModularSystem (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\main.js:692:13)
[1]     at async App.setupIPC (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\main.js:391:9)
[1]     at async App.init (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\main.js:631:9)
[1] [VAULT-MODULE] ⚠️ Continuing with existing vault endpoints
[1] [MODULE:system] Registering simple system endpoints...
[1] [MODULE] Registered endpoint: system:getAPIRegistry
[1] [MODULE] Registered endpoint: system:getPerformanceMetrics
[1] [MODULE] Registered endpoint: system:cleanupMiddleware
[1] [MODULE] Registered endpoint: system:getMonitoringData
[1] [MODULE] Registered endpoint: system:getEndpointMetrics
[1] [MODULE] Registered endpoint: system:resetMonitoring
[1] [MODULE] Registered endpoint: system:getErrorStatistics
[1] [MODULE] Registered endpoint: system:clearErrorHistory
[1] [MODULE:system] Registered 8 simple system endpoints
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] [MODULE:database] Simple Database Module initialized successfully
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] [MODULE:settings] Simple Settings Module initialized successfully
[1] [MODULE:plugins] Simple Plugin Module initialized successfully
[1] [MODULE] Module system initialized successfully
[1] [MODULE:events] Simple Events Module initialized successfully
[1] [MODULE:updater] Simple Updater Module initialized successfully
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] [MODULE:settings] Simple Settings Module initialized successfully
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] [MODULE:database] Simple Database Module initialized successfully
[1] [MODULE:plugins] Simple Plugin Module initialized successfully
[1] [MODULE] Module system initialized successfully
[1] [MODULE:events] Simple Events Module initialized successfully
[1] [MODULE:updater] Simple Updater Module initialized successfully
[1] [MODULE:database] Registering simple database endpoints...
[1] [MODULE] Registered endpoint: db:getConversations
[1] [MODULE] Registered endpoint: db:getConversation
[1] [MODULE] Registered endpoint: db:createConversation
[1] [MODULE] Registered endpoint: db:updateConversation
[1] [MODULE] Registered endpoint: db:deleteConversation
[1] [MODULE] Registered endpoint: db:togglePinConversation
[1] [MODULE] Registered endpoint: db:getMessages
[1] [MODULE] Registered endpoint: db:addMessage
[1] [MODULE] Registered endpoint: db:togglePinMessage
[1] [MODULE] Registered endpoint: db:getFiles
[1] [MODULE] Registered endpoint: db:getFile
[1] [MODULE] Registered endpoint: db:addFile
[1] [MODULE] Registered endpoint: db:updateFile
[1] [MODULE] Registered endpoint: db:deleteFile
[1] [MODULE] Registered endpoint: db:getArtifacts
[1] [MODULE] Registered endpoint: db:addArtifact
[1] [MODULE] Registered endpoint: db:updateArtifact
[1] [MODULE] Registered endpoint: db:removeArtifact
[1] [MODULE] Registered endpoint: db:getConversationArtifacts
[1] [MODULE] Registered endpoint: db:updateMessageIntelligence
[1] [MODULE] Registered endpoint: db:addPinnedIntelligence
[1] [MODULE] Registered endpoint: db:getPinnedIntelligence
[1] [MODULE] Registered endpoint: db:getAllPinnedIntelligence
[1] [MODULE] Registered endpoint: db:searchConversations
[1] [MODULE] Registered endpoint: db:getConversationsWithArtifacts
[1] [MODULE] Registered endpoint: db:getDatabaseHealth
[1] [MODULE] Registered endpoint: db:createBackup
[1] [MODULE] Registered endpoint: db:openAtPath
[1] [MODULE] Registered endpoint: db:safeClose
[1] [MODULE] Registered endpoint: db:prepareForDisconnect
[1] [MODULE] Registered endpoint: db:connectPortableDB
[1] [MODULE] Registered endpoint: db:migrateToPortablePath
[1] [MODULE:database] Registered 32 simple database endpoints
[1] [MODULE:settings] Registering simple settings endpoints...
[1] [MODULE] Registered endpoint: settings:get
[1] [MODULE] Registered endpoint: settings:set
[1] [MODULE] Registered endpoint: settings:setPortableMode
[1] [MODULE] Registered endpoint: settings:setDBPath
[1] [MODULE] Registered endpoint: settings:getAll
[1] [MODULE] Registered endpoint: settings:resetToDefaults
[1] [MODULE:settings] Simple Settings Module endpoints registered successfully
[1] [MODULE:plugins] Registering simple plugin endpoints...
[1] [MODULE] Registered endpoint: plugins:getAll
[1] [MODULE] Registered endpoint: plugins:enable
[1] [MODULE] Registered endpoint: plugins:disable
[1] [MODULE] Registered endpoint: plugins:discover
[1] [MODULE] Registered endpoint: plugins:getConfig
[1] [MODULE] Registered endpoint: plugins:updateConfig
[1] [MODULE] Registered endpoint: plugins:getCapabilities
[1] [MODULE] Registered endpoint: plugins:getAPIEndpoints
[1] [MODULE] Registered endpoint: plugins:getAllAPIEndpoints
[1] [MODULE:plugins] Registered 9 simple plugin endpoints
[1] [SYSTEM-MODULE] ✅ SimpleSystemModule initialized successfully
[1] [MODULE:events] Registering simple events endpoints...
[1] [MODULE] Registered endpoint: events:emit
[1] [MODULE] Registered endpoint: events:subscribe
[1] [MODULE] Registered endpoint: events:unsubscribe
[1] [MODULE:events] Registered 3 simple events endpoints
[1] [MODULE:updater] Registering simple updater endpoints...
[1] [MODULE] Registered endpoint: updater:check-for-updates
[1] [MODULE] Registered endpoint: updater:download-and-install
[1] [MODULE:updater] Registered 2 simple updater endpoints
[1] [MODULE:settings] Registering simple settings endpoints...
[1] [MODULE] Registered endpoint: settings:get
[1] [MODULE] Registered endpoint: settings:set
[1] [MODULE] Registered endpoint: settings:setPortableMode
[1] [MODULE] Registered endpoint: settings:setDBPath
[1] [MODULE] Registered endpoint: settings:getAll
[1] [MODULE] Registered endpoint: settings:resetToDefaults
[1] [MODULE:settings] Simple Settings Module endpoints registered successfully
[1] [MODULE:database] Registering simple database endpoints...
[1] [MODULE] Registered endpoint: db:getConversations
[1] [MODULE] Registered endpoint: db:getConversation
[1] [MODULE] Registered endpoint: db:createConversation
[1] [MODULE] Registered endpoint: db:updateConversation
[1] [MODULE] Registered endpoint: db:deleteConversation
[1] [MODULE] Registered endpoint: db:togglePinConversation
[1] [MODULE] Registered endpoint: db:getMessages
[1] [MODULE] Registered endpoint: db:addMessage
[1] [MODULE] Registered endpoint: db:togglePinMessage
[1] [MODULE] Registered endpoint: db:getFiles
[1] [MODULE] Registered endpoint: db:getFile
[1] [MODULE] Registered endpoint: db:addFile
[1] [MODULE] Registered endpoint: db:updateFile
[1] [MODULE] Registered endpoint: db:deleteFile
[1] [MODULE] Registered endpoint: db:getArtifacts
[1] [MODULE] Registered endpoint: db:addArtifact
[1] [MODULE] Registered endpoint: db:updateArtifact
[1] [MODULE] Registered endpoint: db:removeArtifact
[1] [MODULE] Registered endpoint: db:getConversationArtifacts
[1] [MODULE] Registered endpoint: db:updateMessageIntelligence
[1] [MODULE] Registered endpoint: db:addPinnedIntelligence
[1] [MODULE] Registered endpoint: db:getPinnedIntelligence
[1] [MODULE] Registered endpoint: db:getAllPinnedIntelligence
[1] [MODULE] Registered endpoint: db:searchConversations
[1] [MODULE] Registered endpoint: db:getConversationsWithArtifacts
[1] [MODULE] Registered endpoint: db:getDatabaseHealth
[1] [MODULE] Registered endpoint: db:createBackup
[1] [MODULE] Registered endpoint: db:openAtPath
[1] [MODULE] Registered endpoint: db:safeClose
[1] [MODULE] Registered endpoint: db:prepareForDisconnect
[1] [MODULE] Registered endpoint: db:connectPortableDB
[1] [MODULE] Registered endpoint: db:migrateToPortablePath
[1] [MODULE:database] Registered 32 simple database endpoints
[1] [MODULE:plugins] Registering simple plugin endpoints...
[1] [MODULE] Registered endpoint: plugins:getAll
[1] [MODULE] Registered endpoint: plugins:enable
[1] [MODULE] Registered endpoint: plugins:disable
[1] [MODULE] Registered endpoint: plugins:discover
[1] [MODULE] Registered endpoint: plugins:getConfig
[1] [MODULE] Registered endpoint: plugins:updateConfig
[1] [MODULE] Registered endpoint: plugins:getCapabilities
[1] [MODULE] Registered endpoint: plugins:getAPIEndpoints
[1] [MODULE] Registered endpoint: plugins:getAllAPIEndpoints
[1] [MODULE:plugins] Registered 9 simple plugin endpoints
[1] [SYSTEM-MODULE] ✅ SimpleSystemModule initialized successfully
[1] [MODULE:events] Registering simple events endpoints...
[1] [MODULE] Registered endpoint: events:emit
[1] [MODULE] Registered endpoint: events:subscribe
[1] [MODULE] Registered endpoint: events:unsubscribe
[1] [MODULE:events] Registered 3 simple events endpoints
[1] [MODULE:updater] Registering simple updater endpoints...
[1] [MODULE] Registered endpoint: updater:check-for-updates
[1] [MODULE] Registered endpoint: updater:download-and-install
[1] [MODULE:updater] Registered 2 simple updater endpoints
[1] [MODULE] Module database initialized successfully
[1] [MODULE] Module settings initialized successfully
[1] [MODULE] Module plugins initialized successfully
[1] [MODULE] Module events initialized successfully
[1] [MODULE] Module updater initialized successfully
[1] [MODULE] Module settings initialized successfully
[1] [MODULE] Module database initialized successfully
[1] [MODULE] Module plugins initialized successfully
[1] [MODULE] Module events initialized successfully
[1] [MODULE] Module updater initialized successfully
[1] [DB-MODULE] ✅ SimpleDatabaseModule initialized successfully
[1] [SETTINGS-MODULE] ✅ SimpleSettingsModule initialized successfully
[1] [PLUGIN-MODULE] ✅ SimplePluginModule initialized successfully
[1] [EVENTS-MODULE] ✅ SimpleEventsModule initialized successfully
[1] [UPDATER-MODULE] ✅ SimpleUpdaterModule initialized successfully
[1] [SETTINGS-MODULE] ✅ SimpleSettingsModule initialized successfully
[1] [DB-MODULE] ✅ SimpleDatabaseModule initialized successfully
[1] [PLUGIN-MODULE] ✅ SimplePluginModule initialized successfully
[1] [EVENTS-MODULE] ✅ SimpleEventsModule initialized successfully
[1] [UPDATER-MODULE] ✅ SimpleUpdaterModule initialized successfully
[1] [MODULE:intelligence] Simple Intelligence Module initialized successfully
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] 🔧 FileProcessorService created (plugin-only)
[1] 🔧 Initializing FileProcessorService plugin system...
[1] 🔧 FileSystemManager created with FileProcessorService
[1] [PATH] Using vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] [MODULE:filesystem] Simple Filesystem Module initialized successfully
[1] Initializing database at: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\chatlo-dev.db
[1] Database integrity check passed
[1] Database encoding set to UTF-8 for international character support
[1] Database initialized successfully
[1] 🔧 FileProcessorService created (plugin-only)
[1] 🔧 Initializing FileProcessorService plugin system...
[1] 🔧 FileSystemManager created with FileProcessorService
[1] [PATH] Using vault root path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] [MODULE:filesystem] Simple Filesystem Module initialized successfully
[1] [MODULE:intelligence] Simple Intelligence Module initialized successfully
[1] [MODULE:intelligence] Registering simple intelligence endpoints...
[1] [MODULE] Registered endpoint: intelligence:write
[1] [MODULE] Registered endpoint: intelligence:read
[1] [MODULE] Registered endpoint: intelligence:save
[1] [MODULE] Registered endpoint: intelligence:get
[1] [MODULE] Registered endpoint: intelligence:analyze
[1] [MODULE] Registered endpoint: intelligence:listSessions
[1] [MODULE] Registered endpoint: intelligence:writeSession
[1] [MODULE:intelligence] Registered 7 simple intelligence endpoints
[1] [MODULE:filesystem] Registering simple filesystem endpoints...
[1] [MODULE] Registered endpoint: vault:createDirectory
[1] [MODULE] Registered endpoint: vault:writeFile
[1] [MODULE] Registered endpoint: vault:appendFile
[1] [MODULE] Registered endpoint: vault:readDirectory
[1] [MODULE] Registered endpoint: vault:removeDirectory
[1] [MODULE] Registered endpoint: vault:removeFile
[1] [MODULE] Registered endpoint: vault:scanFolder
[1] [MODULE] Registered endpoint: vault:copyFile
[1] [MODULE] Registered endpoint: vault:pathExists
[1] [MODULE] Registered endpoint: vault:readFile
[1] [MODULE] Registered endpoint: vault:readFileBase64
[1] [MODULE] Registered endpoint: vault:saveVaultRegistry
[1] [MODULE] Registered endpoint: vault:initializeVaultRoot
[1] [MODULE] Registered endpoint: vault:scanContexts
[1] [MODULE] Registered endpoint: files:getVaultRootPath
[1] [MODULE] Registered endpoint: files:setVaultRootPath
[1] [MODULE] Registered endpoint: files:getChatloFolderPath
[1] [MODULE] Registered endpoint: files:setChatloFolderPath
[1] [MODULE] Registered endpoint: files:getIndexedFiles
[1] [MODULE] Registered endpoint: files:getFileProcessorPlugins
[1] [MODULE] Registered endpoint: files:setFileProcessorPluginEnabled
[1] [MODULE] Registered endpoint: files:searchFiles
[1] [MODULE] Registered endpoint: files:getMetadata
[1] [MODULE] Registered endpoint: files:reindexTree
[1] [MODULE] Registered endpoint: files:processFileContent
[1] [MODULE] Registered endpoint: files:indexFile
[1] [MODULE] Registered endpoint: files:indexVaultFile
[1] [MODULE] Registered endpoint: files:indexAllFiles
[1] [MODULE] Registered endpoint: files:copyFileToUploads
[1] [MODULE] Registered endpoint: files:saveContentToVault
[1] [MODULE] Registered endpoint: files:saveContentAsFile
[1] [MODULE] Registered endpoint: files:addFileAttachment
[1] [MODULE] Registered endpoint: files:getFileAttachments
[1] [MODULE] Registered endpoint: files:getMessageFiles
[1] [MODULE] Registered endpoint: files:removeFileAttachment
[1] [MODULE] Registered endpoint: files:deleteFile
[1] [MODULE] Registered endpoint: files:showOpenDialog
[1] [MODULE] Registered endpoint: files:showSaveDialog
[1] [MODULE] Registered endpoint: files:openPDFViewer
[1] [MODULE] Registered endpoint: files:processFile
[1] [MODULE] Registered endpoint: files:getFileContent
[1] [MODULE] Registered endpoint: path:normalize
[1] [MODULE] Registered endpoint: path:join
[1] [MODULE] Registered endpoint: path:getContextDirs
[1] [MODULE] Registered endpoint: shell:openPath
[1] [MODULE] Registered endpoint: shell:showItemInFolder
[1] [MODULE:filesystem] Registered 46 simple filesystem endpoints
[1] [MODULE:filesystem] Registering simple filesystem endpoints...
[1] [MODULE] Registered endpoint: vault:createDirectory
[1] [MODULE] Registered endpoint: vault:writeFile
[1] [MODULE] Registered endpoint: vault:appendFile
[1] [MODULE] Registered endpoint: vault:readDirectory
[1] [MODULE] Registered endpoint: vault:removeDirectory
[1] [MODULE] Registered endpoint: vault:removeFile
[1] [MODULE] Registered endpoint: vault:scanFolder
[1] [MODULE] Registered endpoint: vault:copyFile
[1] [MODULE] Registered endpoint: vault:pathExists
[1] [MODULE] Registered endpoint: vault:readFile
[1] [MODULE] Registered endpoint: vault:readFileBase64
[1] [MODULE] Registered endpoint: vault:saveVaultRegistry
[1] [MODULE] Registered endpoint: vault:initializeVaultRoot
[1] [MODULE] Registered endpoint: vault:scanContexts
[1] [MODULE] Registered endpoint: files:getVaultRootPath
[1] [MODULE] Registered endpoint: files:setVaultRootPath
[1] [MODULE] Registered endpoint: files:getChatloFolderPath
[1] [MODULE] Registered endpoint: files:setChatloFolderPath
[1] [MODULE] Registered endpoint: files:getIndexedFiles
[1] [MODULE] Registered endpoint: files:getFileProcessorPlugins
[1] [MODULE] Registered endpoint: files:setFileProcessorPluginEnabled
[1] [MODULE] Registered endpoint: files:searchFiles
[1] [MODULE] Registered endpoint: files:getMetadata
[1] [MODULE] Registered endpoint: files:reindexTree
[1] [MODULE] Registered endpoint: files:processFileContent
[1] [MODULE] Registered endpoint: files:indexFile
[1] [MODULE] Registered endpoint: files:indexVaultFile
[1] [MODULE] Registered endpoint: files:indexAllFiles
[1] [MODULE] Registered endpoint: files:copyFileToUploads
[1] [MODULE] Registered endpoint: files:saveContentToVault
[1] [MODULE] Registered endpoint: files:saveContentAsFile
[1] [MODULE] Registered endpoint: files:addFileAttachment
[1] [MODULE] Registered endpoint: files:getFileAttachments
[1] [MODULE] Registered endpoint: files:getMessageFiles
[1] [MODULE] Registered endpoint: files:removeFileAttachment
[1] [MODULE] Registered endpoint: files:deleteFile
[1] [MODULE] Registered endpoint: files:showOpenDialog
[1] [MODULE] Registered endpoint: files:showSaveDialog
[1] [MODULE] Registered endpoint: files:openPDFViewer
[1] [MODULE] Registered endpoint: files:processFile
[1] [MODULE] Registered endpoint: files:getFileContent
[1] [MODULE] Registered endpoint: path:normalize
[1] [MODULE] Registered endpoint: path:join
[1] [MODULE] Registered endpoint: path:getContextDirs
[1] [MODULE] Registered endpoint: shell:openPath
[1] [MODULE] Registered endpoint: shell:showItemInFolder
[1] [MODULE:filesystem] Registered 46 simple filesystem endpoints
[1] [MODULE:intelligence] Registering simple intelligence endpoints...
[1] [MODULE] Registered endpoint: intelligence:write
[1] [MODULE] Registered endpoint: intelligence:read
[1] [MODULE] Registered endpoint: intelligence:save
[1] [MODULE] Registered endpoint: intelligence:get
[1] [MODULE] Registered endpoint: intelligence:analyze
[1] [MODULE] Registered endpoint: intelligence:listSessions
[1] [MODULE] Registered endpoint: intelligence:writeSession
[1] [MODULE:intelligence] Registered 7 simple intelligence endpoints
[1] [MODULE] Module intelligence initialized successfully
[1] Plugin registered: TextPlugin v1.0.0
[1] [MODULE] Module filesystem initialized successfully
[1] Plugin registered: TextPlugin v1.0.0
[1] [MODULE] Module filesystem initialized successfully
[1] [MODULE] Module intelligence initialized successfully
[1] [INTEL-MODULE] ✅ SimpleIntelligenceModule initialized successfully
[1] [FILESYSTEM-MODULE] ✅ SimpleFilesystemModule initialized successfully
[1] [FILESYSTEM-MODULE] ✅ SimpleFilesystemModule initialized successfully
[1] [INTEL-MODULE] ✅ SimpleIntelligenceModule initialized successfully
[1] Plugin registered: MarkdownPlugin v1.0.0
[1] Plugin registered: MarkdownPlugin v1.0.0
[1] [MODULAR-REGISTRY] ✅ All critical modules initialized successfully
[1] [MODULAR-REGISTRY] 🎯 Phase 1 complete: Full modular system running alongside APIRegistry
[1] [MAIN] Modular registry system initialized successfully
[1] [MAIN] Initializing API registry with registered endpoints...
[1] [APIRegistry] Initializing API endpoints...
[1] [APIRegistry] Categories: [
[1]   'system',       'db',
[1]   'settings',     'plugins',
[1]   'events',       'updater',
[1]   'intelligence', 'vault',
[1]   'files',        'path',
[1]   'shell'
[1] ]
[1] [APIRegistry] Processing category: system
[1] [APIRegistry] Endpoints in system: [
[1]   'getAPIRegistry',
[1]   'getPerformanceMetrics',
[1]   'cleanupMiddleware',
[1]   'getMonitoringData',
[1]   'getEndpointMetrics',
[1]   'resetMonitoring',
[1]   'getErrorStatistics',
[1]   'clearErrorHistory'
[1] ]
[1] [APIRegistry] Registering IPC handler: system:getAPIRegistry
[1] [APIRegistry] Registering IPC handler: system:getPerformanceMetrics
[1] [APIRegistry] Registering IPC handler: system:cleanupMiddleware
[1] [APIRegistry] Registering IPC handler: system:getMonitoringData
[1] [APIRegistry] Registering IPC handler: system:getEndpointMetrics
[1] [APIRegistry] Registering IPC handler: system:resetMonitoring
[1] [APIRegistry] Registering IPC handler: system:getErrorStatistics
[1] [APIRegistry] Registering IPC handler: system:clearErrorHistory
[1] [APIRegistry] Processing category: db
[1] [APIRegistry] Endpoints in db: [
[1]   'getConversations',
[1]   'getConversation',
[1]   'createConversation',
[1]   'updateConversation',
[1]   'deleteConversation',
[1]   'togglePinConversation',
[1]   'getMessages',
[1]   'addMessage',
[1]   'togglePinMessage',
[1]   'getFiles',
[1]   'getFile',
[1]   'addFile',
[1]   'updateFile',
[1]   'deleteFile',
[1]   'getArtifacts',
[1]   'addArtifact',
[1]   'updateArtifact',
[1]   'removeArtifact',
[1]   'getConversationArtifacts',
[1]   'updateMessageIntelligence',
[1]   'addPinnedIntelligence',
[1]   'getPinnedIntelligence',
[1]   'getAllPinnedIntelligence',
[1]   'searchConversations',
[1]   'getConversationsWithArtifacts',
[1]   'getDatabaseHealth',
[1]   'createBackup',
[1]   'openAtPath',
[1]   'safeClose',
[1]   'prepareForDisconnect',
[1]   'connectPortableDB',
[1]   'migrateToPortablePath'
[1] ]
[1] [APIRegistry] Registering IPC handler: db:getConversations
[1] [APIRegistry] Registering IPC handler: db:getConversation
[1] [APIRegistry] Registering IPC handler: db:createConversation
[1] [APIRegistry] Registering IPC handler: db:updateConversation
[1] [APIRegistry] Registering IPC handler: db:deleteConversation
[1] [APIRegistry] Registering IPC handler: db:togglePinConversation
[1] [APIRegistry] Registering IPC handler: db:getMessages
[1] [APIRegistry] Registering IPC handler: db:addMessage
[1] [APIRegistry] Registering IPC handler: db:togglePinMessage
[1] [APIRegistry] Registering IPC handler: db:getFiles
[1] [APIRegistry] Registering IPC handler: db:getFile
[1] [APIRegistry] Registering IPC handler: db:addFile
[1] [APIRegistry] Registering IPC handler: db:updateFile
[1] [APIRegistry] Registering IPC handler: db:deleteFile
[1] [APIRegistry] Registering IPC handler: db:getArtifacts
[1] [APIRegistry] Registering IPC handler: db:addArtifact
[1] [APIRegistry] Registering IPC handler: db:updateArtifact
[1] [APIRegistry] Registering IPC handler: db:removeArtifact
[1] [APIRegistry] Registering IPC handler: db:getConversationArtifacts
[1] [APIRegistry] Registering IPC handler: db:updateMessageIntelligence
[1] [APIRegistry] Registering IPC handler: db:addPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:getPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:getAllPinnedIntelligence
[1] [APIRegistry] Registering IPC handler: db:searchConversations
[1] [APIRegistry] Registering IPC handler: db:getConversationsWithArtifacts
[1] [APIRegistry] Registering IPC handler: db:getDatabaseHealth
[1] [APIRegistry] Registering IPC handler: db:createBackup
[1] [APIRegistry] Registering IPC handler: db:openAtPath
[1] [APIRegistry] Registering IPC handler: db:safeClose
[1] [APIRegistry] Registering IPC handler: db:prepareForDisconnect
[1] [APIRegistry] Registering IPC handler: db:connectPortableDB
[1] [APIRegistry] Registering IPC handler: db:migrateToPortablePath
[1] [APIRegistry] Processing category: settings
[1] [APIRegistry] Endpoints in settings: [
[1]   'get',
[1]   'set',
[1]   'setPortableMode',
[1]   'setDBPath',
[1]   'getAll',
[1]   'resetToDefaults'
[1] ]
[1] [APIRegistry] Registering IPC handler: settings:get
[1] [APIRegistry] Registering IPC handler: settings:set
[1] [APIRegistry] Registering IPC handler: settings:setPortableMode
[1] [APIRegistry] Registering IPC handler: settings:setDBPath
[1] [APIRegistry] Registering IPC handler: settings:getAll
[1] [APIRegistry] Registering IPC handler: settings:resetToDefaults
[1] [APIRegistry] Processing category: plugins
[1] [APIRegistry] Endpoints in plugins: [
[1]   'getAll',
[1]   'enable',
[1]   'disable',
[1]   'discover',
[1]   'getConfig',
[1]   'updateConfig',
[1]   'getCapabilities',
[1]   'getAPIEndpoints',
[1]   'getAllAPIEndpoints'
[1] ]
[1] [APIRegistry] Registering IPC handler: plugins:getAll
[1] [APIRegistry] Registering IPC handler: plugins:enable
[1] [APIRegistry] Registering IPC handler: plugins:disable
[1] [APIRegistry] Registering IPC handler: plugins:discover
[1] [APIRegistry] Registering IPC handler: plugins:getConfig
[1] [APIRegistry] Registering IPC handler: plugins:updateConfig
[1] [APIRegistry] Registering IPC handler: plugins:getCapabilities
[1] [APIRegistry] Registering IPC handler: plugins:getAPIEndpoints
[1] [APIRegistry] Registering IPC handler: plugins:getAllAPIEndpoints
[1] [APIRegistry] Processing category: events
[1] [APIRegistry] Endpoints in events: [ 'emit', 'subscribe', 'unsubscribe' ]
[1] [APIRegistry] Registering IPC handler: events:emit
[1] [APIRegistry] Registering IPC handler: events:subscribe
[1] [APIRegistry] Registering IPC handler: events:unsubscribe
[1] [APIRegistry] Processing category: updater
[1] [APIRegistry] Endpoints in updater: [ 'check-for-updates', 'download-and-install' ]
[1] [APIRegistry] Registering IPC handler: updater:check-for-updates
[1] [APIRegistry] Registering IPC handler: updater:download-and-install
[1] [APIRegistry] Processing category: intelligence
[1] [APIRegistry] Endpoints in intelligence: [
[1]   'write',
[1]   'read',
[1]   'save',
[1]   'get',
[1]   'analyze',
[1]   'listSessions',
[1]   'writeSession'
[1] ]
[1] [APIRegistry] Registering IPC handler: intelligence:write
[1] [APIRegistry] Registering IPC handler: intelligence:read
[1] [APIRegistry] Registering IPC handler: intelligence:save
[1] [APIRegistry] Registering IPC handler: intelligence:get
[1] [APIRegistry] Registering IPC handler: intelligence:analyze
[1] [APIRegistry] Registering IPC handler: intelligence:listSessions
[1] [APIRegistry] Registering IPC handler: intelligence:writeSession
[1] [APIRegistry] Processing category: vault
[1] [APIRegistry] Endpoints in vault: [
[1]   'createDirectory',
[1]   'writeFile',
[1]   'appendFile',
[1]   'readDirectory',
[1]   'removeDirectory',
[1]   'removeFile',
[1]   'scanFolder',
[1]   'copyFile',
[1]   'pathExists',
[1]   'readFile',
[1]   'readFileBase64',
[1]   'saveVaultRegistry',
[1]   'initializeVaultRoot',
[1]   'scanContexts'
[1] ]
[1] [APIRegistry] Registering IPC handler: vault:createDirectory
[1] [APIRegistry] Registering IPC handler: vault:writeFile
[1] [APIRegistry] Registering IPC handler: vault:appendFile
[1] [APIRegistry] Registering IPC handler: vault:readDirectory
[1] [APIRegistry] Registering IPC handler: vault:removeDirectory
[1] [APIRegistry] Registering IPC handler: vault:removeFile
[1] [APIRegistry] Registering IPC handler: vault:scanFolder
[1] [APIRegistry] Registering IPC handler: vault:copyFile
[1] [APIRegistry] Registering IPC handler: vault:pathExists
[1] [APIRegistry] Registering IPC handler: vault:readFile
[1] [APIRegistry] Registering IPC handler: vault:readFileBase64
[1] [APIRegistry] Registering IPC handler: vault:saveVaultRegistry
[1] [APIRegistry] Registering IPC handler: vault:initializeVaultRoot
[1] [APIRegistry] Registering IPC handler: vault:scanContexts
[1] [APIRegistry] Processing category: files
[1] [APIRegistry] Endpoints in files: [
[1]   'getVaultRootPath',
[1]   'setVaultRootPath',
[1]   'getChatloFolderPath',
[1]   'setChatloFolderPath',
[1]   'getIndexedFiles',
[1]   'getFileProcessorPlugins',
[1]   'setFileProcessorPluginEnabled',
[1]   'searchFiles',
[1]   'getMetadata',
[1]   'reindexTree',
[1]   'processFileContent',
[1]   'indexFile',
[1]   'indexVaultFile',
[1]   'indexAllFiles',
[1]   'copyFileToUploads',
[1]   'saveContentToVault',
[1]   'saveContentAsFile',
[1]   'addFileAttachment',
[1]   'getFileAttachments',
[1]   'getMessageFiles',
[1]   'removeFileAttachment',
[1]   'deleteFile',
[1]   'showOpenDialog',
[1]   'showSaveDialog',
[1]   'openPDFViewer',
[1]   'processFile',
[1]   'getFileContent'
[1] ]
[1] [APIRegistry] Registering IPC handler: files:getVaultRootPath
[1] [APIRegistry] Registering IPC handler: files:setVaultRootPath
[1] [APIRegistry] Registering IPC handler: files:getChatloFolderPath
[1] [APIRegistry] Registering IPC handler: files:setChatloFolderPath
[1] [APIRegistry] Registering IPC handler: files:getIndexedFiles
[1] [APIRegistry] Registering IPC handler: files:getFileProcessorPlugins
[1] [APIRegistry] Registering IPC handler: files:setFileProcessorPluginEnabled
[1] [APIRegistry] Registering IPC handler: files:searchFiles
[1] [APIRegistry] Registering IPC handler: files:getMetadata
[1] [APIRegistry] Registering IPC handler: files:reindexTree
[1] [APIRegistry] Registering IPC handler: files:processFileContent
[1] [APIRegistry] Registering IPC handler: files:indexFile
[1] [APIRegistry] Registering IPC handler: files:indexVaultFile
[1] [APIRegistry] Registering IPC handler: files:indexAllFiles
[1] [APIRegistry] Registering IPC handler: files:copyFileToUploads
[1] [APIRegistry] Registering IPC handler: files:saveContentToVault
[1] [APIRegistry] Registering IPC handler: files:saveContentAsFile
[1] [APIRegistry] Registering IPC handler: files:addFileAttachment
[1] [APIRegistry] Registering IPC handler: files:getFileAttachments
[1] [APIRegistry] Registering IPC handler: files:getMessageFiles
[1] [APIRegistry] Registering IPC handler: files:removeFileAttachment
[1] [APIRegistry] Registering IPC handler: files:deleteFile
[1] [APIRegistry] Registering IPC handler: files:showOpenDialog
[1] [APIRegistry] Registering IPC handler: files:showSaveDialog
[1] [APIRegistry] Registering IPC handler: files:openPDFViewer
[1] [APIRegistry] Registering IPC handler: files:processFile
[1] [APIRegistry] Registering IPC handler: files:getFileContent
[1] [APIRegistry] Processing category: path
[1] [APIRegistry] Endpoints in path: [ 'normalize', 'join', 'getContextDirs' ]
[1] [APIRegistry] Registering IPC handler: path:normalize
[1] [APIRegistry] Registering IPC handler: path:join
[1] [APIRegistry] Registering IPC handler: path:getContextDirs
[1] [APIRegistry] Processing category: shell
[1] [APIRegistry] Endpoints in shell: [ 'openPath', 'showItemInFolder' ]
[1] [APIRegistry] Registering IPC handler: shell:openPath
[1] [APIRegistry] Registering IPC handler: shell:showItemInFolder
[1] [MAIN] API registry initialized successfully
[1] [ELECTRON] Custom file protocol registered
[1] [PLUGIN] Starting plugin initialization...
[1] [PLUGIN] Discovering plugins in directories: [
[1]   'C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\plugins',
[1]   'C:\\Users\\<USER>\\AppData\\Roaming\\chatlo\\plugins'
[1] ]
[1] [PLUGIN] Scanning directory: C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] Plugin registered: HTMLPlugin v1.0.0
[1] Plugin registered: HTMLPlugin v1.0.0
[1] [PROCESSING] OCR plugin disabled from settings
[1] Plugin registered: CSVPlugin v1.0.0
[1] Plugin registered: CSVPlugin v1.0.0
[1] Plugin registered: TextPlugin v1.0.0
[1] Plugin registered: BasicImagePlugin v1.0.0
[1] Plugin registered: BasicImagePlugin v1.0.0
[1] Plugin registered: MarkdownPlugin v1.0.0
[1] Plugin registered: HTMLPlugin v1.0.0
[1] PDFPlugin initialized successfully
[1] PDFPlugin initialized successfully
[1] Plugin registered: CSVPlugin v1.0.0
[1] Plugin registered: PDFPlugin v1.0.0
[1] Plugin registered: PDFPlugin v1.0.0
[1] Plugin registered: BasicImagePlugin v1.0.0
[1] WordPlugin initialized successfully with officeParser library
[1] WordPlugin initialized successfully with officeParser library
[1] Plugin registered: WordPlugin v2.0.0
[1] Plugin registered: WordPlugin v2.0.0
[1] ExcelPlugin initialized successfully with officeParser library
[1] ExcelPlugin initialized successfully with officeParser library
[1] Plugin registered: ExcelPlugin v2.0.0
[1] Plugin registered: ExcelPlugin v2.0.0
[1] PDFPlugin initialized successfully
[1] Plugin registered: PDFPlugin v1.0.0
[1] PowerPointPlugin initialized successfully with officeParser library
[1] PowerPointPlugin initialized successfully with officeParser library
[1] Plugin registered: PowerPointPlugin v2.0.0
[1] Plugin registered: PowerPointPlugin v2.0.0
[1] WordPlugin initialized successfully with officeParser library
[1] Plugin registered: WordPlugin v2.0.0
[1] ImagePlugin (Sharp) initialized successfully
[1] ImagePlugin (Sharp) initialized successfully
[1] Plugin registered: ImagePlugin v1.0.0
[1] Plugin registered: ImagePlugin v1.0.0
[1] ExcelPlugin initialized successfully with officeParser library
[1] Plugin registered: ExcelPlugin v2.0.0
[1] OCRPlugin (Tesseract.js) initialized successfully
[1] OCRPlugin (Tesseract.js) initialized successfully
[1] Plugin registered: OCRPlugin v1.0.0
[1] Plugin registered: OCRPlugin v1.0.0
[1] PowerPointPlugin initialized successfully with officeParser library
[1] Plugin registered: PowerPointPlugin v2.0.0
[1] PluginFileProcessorService initialized successfully
[1] PluginFileProcessorService initialized successfully
[1] 🔧 FileProcessorService plugin system initialized successfully
[1] 🔧 FileProcessorService plugin system initialized successfully
[1] ImagePlugin (Sharp) initialized successfully
[1] Plugin registered: ImagePlugin v1.0.0
[1] OCRPlugin (Tesseract.js) initialized successfully
[1] Plugin registered: OCRPlugin v1.0.0
[1] PluginFileProcessorService initialized successfully
[1] [PROCESSING] PluginFileProcessor initialized successfully - Office plugins loaded
[1] [PLUGIN] Found 5 entries in C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] [PLUGIN] Found 0 plugins in C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\plugins
[1] [PLUGIN] Scanning directory: C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Directory does not exist: C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Found 0 plugins in C:\Users\<USER>\AppData\Roaming\chatlo\plugins
[1] [PLUGIN] Total plugins discovered: 0
[1] [PLUGIN] Discovered 0 plugins
[1] [PLUGIN] No plugins found, skipping plugin loading
[1] [PLUGIN] Plugin initialization completed
[1] [API] db:getConversations - Request: []
[1] [API] db:getConversations - Duration: 0ms
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] [API] settings:get - Request: ["app-settings"]
[1] [SETTINGS] Getting setting: app-settings
[1] [SETTINGS] Retrieved setting app-settings: {
[1]   temperature: 0.1,
[1]   maxTokens: 3891,
[1]   theme: 'dark',
[1]   topP: 0.9,
[1]   topK: 20,
[1]   frequencyPenalty: 0,
[1]   presencePenalty: 0,
[1]   systemPrompt: '',
[1]   favoriteModels: [],
[1]   modelFilter: 'all',
[1]   openRouterApiKey: '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',
[1]   selectedModel: 'ollama:gemma3:latest',
[1]   securityLevel: 'disabled'
[1] }
[1] [API] settings:get - Duration: 1ms
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] [API] settings:get - Request: ["model-manifest-version"]
[1] [SETTINGS] Getting setting: model-manifest-version
[1] [SETTINGS] Retrieved setting model-manifest-version: 2025.07.16
[1] [API] settings:get - Duration: 1ms
[1] [24436:0826/153657.717:ERROR:CONSOLE:1] "Request Autofill.enable failed. {"code":-32601,"message":"'Autofill.enable' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[1] [24436:0826/153657.718:ERROR:CONSOLE:1] "Request Autofill.setAddresses failed. {"code":-32601,"message":"'Autofill.setAddresses' wasn't found"}", source: devtools://devtools/bundled/core/protocol_client/protocol_client.js (1)
[1] [API] files:getIndexedFiles - Request: []
[1] [API] files:getIndexedFiles - Duration: 29ms
[1] [API] settings:get - Request: ["vault-root-path"]
[1] [SETTINGS] Getting setting: vault-root-path
[1] [SETTINGS] Retrieved setting vault-root-path: C:\Users\<USER>\Documents\Post-Kernel-Test3
[1] [API] settings:get - Duration: 1ms
[1] [API] settings:get - Request: ["portable-mode-enabled"]
[1] [SETTINGS] Getting setting: portable-mode-enabled
[1] [SETTINGS] Retrieved setting portable-mode-enabled: null
[1] [API] settings:get - Duration: 0ms
[1] [API] settings:get - Request: ["securityLevel"]
[1] [SETTINGS] Getting setting: securityLevel
[1] [SETTINGS] Retrieved setting securityLevel: disabled
[1] [API] settings:get - Duration: 0ms
[1] [API] settings:get - Request: ["allowedVaultPatterns"]
[1] [SETTINGS] Getting setting: allowedVaultPatterns
[1] [SETTINGS] Retrieved setting allowedVaultPatterns: null
[1] [API] settings:get - Duration: 1ms
[1] [API] files:showOpenDialog - Request: [{"title":"Select Vault Root Folder","properties":["openDirectory"],"defaultPath":"C:\\Users\\<USER>\\Documents\\Post-Kernel-Test3"}]
[1] [API] files:showOpenDialog - Duration: 0ms
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] [API] db:getConversationArtifacts - Request: ["dda9dbde-f06e-4fb1-9b51-3c29a354212b"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["18a35c60-3411-4c2f-b458-2589c3f9fb5b"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["22fe90d7-23ce-4dab-a3c0-e83f383bf983"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["264f9a94-7591-4d9a-8be7-8b994026a8ae"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["d417c163-830f-4243-893d-08fc6b992d30"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["2cec9edb-c33c-4785-aa76-ad403a98aa47"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["5ec83233-2f3b-4a63-9b17-7fcc8b9060cb"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["3f8634f0-073f-416d-8cce-3403ee1125e3"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["d527b0c9-3208-4993-ba05-476b6eacd809"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["7244c8b4-cf64-4349-8d85-1a83df332fc3"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["e74f047d-5278-481e-a0a2-0b4c0947d9a1"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["d09d7903-a003-4eb4-bc1e-f9a60a58d742"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["71e62d1e-c99c-4ceb-9454-15536fd9d7e7"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["2bdab99c-3814-450f-b45b-5171f10eabe8"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["6c3c5c04-c63d-49dd-ae40-d9d0650b28a6"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["aeeb67a0-d79c-49a5-82a0-0a82b8dece96"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["36b130f4-566e-485c-a01a-a1f30f58073f"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["5690aafd-4871-4d4a-945d-34c652a6dc7a"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["6ef5e4d7-b320-4614-8f80-509fc58219a1"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["4804e6ea-2615-4526-afab-c143ae59c4a3"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["6656676c-191e-49e1-970d-f4e28d518b40"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["3b621757-404a-44f0-b080-0279efabdaa1"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["0abe02c6-0ca9-4009-96ef-94fd9993df01"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["eb5af8bb-d4ec-411d-a171-1a993665138b"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["7f047d24-b8f7-42b3-a5dd-7432bfa34579"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["7dc73751-a90e-4256-a1d6-00c297f84aec"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["04ef3e83-2f73-4a53-8739-456e4a3097c4"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["4c793007-a813-4711-97a2-e07c3efba8a2"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["7d71c7ad-3a0d-4708-b867-8e7cffc70796"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["03b61260-e482-4dc8-aa3b-46b66822aa56"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["a500a06b-d8e9-43a4-8084-c975c4b84ac9"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["2f017396-bebf-4e34-8680-a2c5734cca30"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["a7accb83-2d6e-45e6-9703-dac20e9c7daf"]
[1] [API] db:getConversationArtifacts - Duration: 1ms
[1] [API] db:getConversationArtifacts - Request: ["0b9d9b29-faa9-4d72-8c43-dabec833a922"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] [API] db:getConversationArtifacts - Request: ["mcf43hkmjml3qjvapv"]
[1] [API] db:getConversationArtifacts - Duration: 0ms
[1] Error occurred in handler for 'vault:getVaultRegistry': Error: No handler registered for 'vault:getVaultRegistry'
[1]     at Session.<anonymous> (node:electron/js2c/browser_init:2:107137)
[1]     at Session.emit (node:events:518:28)
[1] [API] events:subscribe - Request: ["task",null]
[1] [MEDIUM] UNKNOWN_ERROR: Invalid callback {
[1]   category: 'events',
[1]   endpoint: 'subscribe',
[1]   timestamp: '2025-08-26T07:37:47.886Z',
[1]   details: {
[1]     originalError: 'Error',
[1]     stack: 'Error: Invalid callback\n' +
[1]       '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +     
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]   },
[1]   context: { args: [ 'task', undefined ], channelName: 'events:subscribe' }
[1] }
[1] [API ALERT] MEDIUM: High error rate detected: 100.00% for events:subscribe
[1] [API Error] events:subscribe: StructuredAPIError: Invalid callback
[1]     at ErrorHandler.createStructuredError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:146:16)
[1]     at ErrorHandler.handleError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:161:38)
[1]     at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:166:78
[1]     at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024) {
[1]   structuredError: {
[1]     code: 'UNKNOWN_ERROR',
[1]     message: 'Invalid callback',
[1]     details: {
[1]       originalError: 'Error',
[1]       stack: 'Error: Invalid callback\n' +
[1]         '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +   
[1]         '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]         '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]     },
[1]     timestamp: '2025-08-26T07:37:47.886Z',
[1]     severity: 'medium',
[1]     category: 'events',
[1]     endpoint: 'subscribe',
[1]     userId: undefined,
[1]     sessionId: undefined,
[1]     stackTrace: 'StructuredAPIError: Invalid callback\n' +
[1]       '    at ErrorHandler.createStructuredError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:146:16)\n' +        
[1]       '    at ErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:161:38)\n' +
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:166:78\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)',
[1]     context: { args: [Array], channelName: 'events:subscribe' }
[1]   },
[1]   cause: Error: Invalid callback
[1]       at Object.validator (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\events\SimpleEventsModule.js:87:27)
[1]       at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:148:38
[1]       at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)
[1] }
[1] [API] db:getConversations - Request: []
[1] [API] db:getConversations - Duration: 1ms
[1] [API] events:subscribe - Request: ["task",null]
[1] [MEDIUM] UNKNOWN_ERROR: Invalid callback {
[1]   category: 'events',
[1]   endpoint: 'subscribe',
[1]   timestamp: '2025-08-26T07:37:48.964Z',
[1]   details: {
[1]     originalError: 'Error',
[1]     stack: 'Error: Invalid callback\n' +
[1]       '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +     
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]   },
[1]   context: { args: [ 'task', undefined ], channelName: 'events:subscribe' }
[1] }
[1] [API Error] events:subscribe: StructuredAPIError: Invalid callback
[1]     at ErrorHandler.createStructuredError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:146:16)
[1]     at ErrorHandler.handleError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:161:38)
[1]     at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:166:78
[1]     at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024) {
[1]   structuredError: {
[1]     code: 'UNKNOWN_ERROR',
[1]     message: 'Invalid callback',
[1]     details: {
[1]       originalError: 'Error',
[1]       stack: 'Error: Invalid callback\n' +
[1]         '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +   
[1]         '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]         '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]     },
[1]     timestamp: '2025-08-26T07:37:48.964Z',
[1]     severity: 'medium',
[1]     category: 'events',
[1]     endpoint: 'subscribe',
[1]     userId: undefined,
[1]     sessionId: undefined,
[1]     stackTrace: 'StructuredAPIError: Invalid callback\n' +
[1]       '    at ErrorHandler.createStructuredError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:146:16)\n' +        
[1]       '    at ErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:161:38)\n' +
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:166:78\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)',
[1]     context: { args: [Array], channelName: 'events:subscribe' }
[1]   },
[1]   cause: Error: Invalid callback
[1]       at Object.validator (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\events\SimpleEventsModule.js:87:27)
[1]       at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:148:38
[1]       at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)
[1] }
[1] [API] db:getMessages - Request: ["dda9dbde-f06e-4fb1-9b51-3c29a354212b"]
[1] [API] db:getMessages - Duration: 1ms
[1] [API] db:getMessages - Request: ["dda9dbde-f06e-4fb1-9b51-3c29a354212b"]
[1] [API] db:getMessages - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["af720f43-00fb-41d0-bff9-e8342c2b53e3"]
[1] [API] files:getMessageFiles - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["b22685ee-f522-4bf2-b016-9b16ed36b83d"]
[1] [API] files:getMessageFiles - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["83502f8a-df99-4db9-8008-060d34394e7b"]
[1] [API] files:getMessageFiles - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["af720f43-00fb-41d0-bff9-e8342c2b53e3"]
[1] [API] files:getMessageFiles - Duration: 0ms
[1] [API] files:getMessageFiles - Request: ["b22685ee-f522-4bf2-b016-9b16ed36b83d"]
[1] [API] files:getMessageFiles - Duration: 1ms
[1] [API] files:getMessageFiles - Request: ["83502f8a-df99-4db9-8008-060d34394e7b"]
[1] [API] files:getMessageFiles - Duration: 0ms
[1] [API] events:subscribe - Request: ["task",null]
[1] [MEDIUM] UNKNOWN_ERROR: Invalid callback {
[1]   category: 'events',
[1]   endpoint: 'subscribe',
[1]   timestamp: '2025-08-26T07:37:50.970Z',
[1]   details: {
[1]     originalError: 'Error',
[1]     stack: 'Error: Invalid callback\n' +
[1]       '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +     
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]   },
[1]   context: { args: [ 'task', undefined ], channelName: 'events:subscribe' }
[1] }
[1] [API Error] events:subscribe: StructuredAPIError: Invalid callback
[1]     at ErrorHandler.createStructuredError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:146:16)
[1]     at ErrorHandler.handleError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:161:38)
[1]     at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:166:78
[1]     at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024) {
[1]   structuredError: {
[1]     code: 'UNKNOWN_ERROR',
[1]     message: 'Invalid callback',
[1]     details: {
[1]       originalError: 'Error',
[1]       stack: 'Error: Invalid callback\n' +
[1]         '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +   
[1]         '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]         '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]     },
[1]     timestamp: '2025-08-26T07:37:50.970Z',
[1]     severity: 'medium',
[1]     category: 'events',
[1]     endpoint: 'subscribe',
[1]     userId: undefined,
[1]     sessionId: undefined,
[1]     stackTrace: 'StructuredAPIError: Invalid callback\n' +
[1]       '    at ErrorHandler.createStructuredError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:146:16)\n' +        
[1]       '    at ErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:161:38)\n' +
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:166:78\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)',
[1]     context: { args: [Array], channelName: 'events:subscribe' }
[1]   },
[1]   cause: Error: Invalid callback
[1]       at Object.validator (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\events\SimpleEventsModule.js:87:27)
[1]       at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:148:38
[1]       at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)
[1] }
[1] [API] events:subscribe - Request: ["task",null]
[1] [MEDIUM] UNKNOWN_ERROR: Invalid callback {
[1]   category: 'events',
[1]   endpoint: 'subscribe',
[1]   timestamp: '2025-08-26T07:37:53.977Z',
[1]   details: {
[1]     originalError: 'Error',
[1]     stack: 'Error: Invalid callback\n' +
[1]       '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +     
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]   },
[1]   context: { args: [ 'task', undefined ], channelName: 'events:subscribe' }
[1] }
[1] [API Error] events:subscribe: StructuredAPIError: Invalid callback
[1]     at ErrorHandler.createStructuredError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:146:16)
[1]     at ErrorHandler.handleError (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\errorHandling.js:161:38)
[1]     at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:166:78
[1]     at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024) {
[1]   structuredError: {
[1]     code: 'UNKNOWN_ERROR',
[1]     message: 'Invalid callback',
[1]     details: {
[1]       originalError: 'Error',
[1]       stack: 'Error: Invalid callback\n' +
[1]         '    at Object.validator (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\modules\\events\\SimpleEventsModule.js:87:27)\n' +   
[1]         '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:148:38\n' +
[1]         '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)'
[1]     },
[1]     timestamp: '2025-08-26T07:37:53.977Z',
[1]     severity: 'medium',
[1]     category: 'events',
[1]     endpoint: 'subscribe',
[1]     userId: undefined,
[1]     sessionId: undefined,
[1]     stackTrace: 'StructuredAPIError: Invalid callback\n' +
[1]       '    at ErrorHandler.createStructuredError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:146:16)\n' +        
[1]       '    at ErrorHandler.handleError (C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\errorHandling.js:161:38)\n' +
[1]       '    at C:\\Users\\<USER>\\Documents\\augment-projects\\chat-locally\\chatlo\\dist\\api\\APIRegistry.js:166:78\n' +
[1]       '    at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)',
[1]     context: { args: [Array], channelName: 'events:subscribe' }
[1]   },
[1]   cause: Error: Invalid callback
[1]       at Object.validator (C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\modules\events\SimpleEventsModule.js:87:27)
[1]       at C:\Users\<USER>\Documents\augment-projects\chat-locally\chatlo\dist\api\APIRegistry.js:148:38
[1]       at async Session.<anonymous> (node:electron/js2c/browser_init:2:107024)
[1] }
[1] [SYSTEM HEALTH] Status: degraded, Er