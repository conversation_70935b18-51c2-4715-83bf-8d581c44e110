{"name": "chatlo", "version": "1.0.0", "description": "AI Chat App with OpenRouter Integration", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on tcp:5173 && npm run dev:electron-no-check\"", "dev:electron-no-check": "cross-env NODE_ENV=development electron . --no-type-check", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && vite build && tsc -p electron", "build-with-check": "npm run type-check && npm run clean && vite build && tsc -p electron", "build:electron": "tsc -p electron", "build:package": "electron-builder", "build:publish": "npm run build && electron-builder --publish=always", "build:draft": "npm run build && electron-builder --publish=never", "rebuild": "npx electron-rebuild", "postinstall": "electron-builder install-app-deps", "preview": "vite preview", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "quality-check": "npm run type-check", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .ts,.tsx --fix", "lint:fontawesome": "eslint src --ext .ts,.tsx --rule 'no-restricted-imports: error' --no-eslintrc --parser @typescript-eslint/parser", "pre-commit": "powershell -ExecutionPolicy Bypass -File ./scripts/pre-commit-check.ps1"}, "keywords": ["electron", "react", "typescript", "ai", "chat", "openrouter"], "author": "chatlo", "license": "MIT", "build": {"appId": "com.chatlo.app", "productName": "Cha<PERSON><PERSON>", "icon": "public/icons/icon", "directories": {"output": "release"}, "files": ["dist/**/*", "!dist/**/*.map", "node_modules/**/*", "!node_modules/mermaid/**", "!node_modules/@fortawesome/**", "!node_modules/pdfjs-dist/cmaps/**", "!node_modules/pdfjs-dist/standard_fonts/**", "!node_modules/@types/**", "!node_modules/typescript/**", "!node_modules/vite/**", "!node_modules/@vitejs/**", "!node_modules/esbuild/**", "!node_modules/rollup/**", "!node_modules/@img/sharp-darwin-*/**/*", "!node_modules/@img/sharp-linux-*/**/*"], "asarUnpack": ["node_modules/better-sqlite3/**/*"], "compression": "maximum", "publish": {"provider": "github", "owner": "chatlo", "repo": "chatlo"}, "mac": {"category": "public.app-category.productivity", "icon": "public/icons/icon.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "public/icons/icon.png", "target": [{"target": "nsis", "arch": ["x64"]}]}, "linux": {"icon": "public/icons/icon.png", "target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "extraResources": [], "extraFiles": []}, "dependencies": {"@emnapi/runtime": "^1.4.4", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.3", "better-sqlite3": "^12.1.1", "chokidar": "^4.0.3", "docx-preview": "^0.3.6", "electron-updater": "^6.6.2", "exceljs": "^4.4.0", "jszip": "^3.10.1", "mermaid": "^11.9.0", "mime-types": "^3.0.1", "officegen": "^0.6.5", "officeparser": "^5.2.0", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.4.54", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.3", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "xlsx-populate": "^1.21.0", "zustand": "^5.0.5"}, "optionalDependencies": {"mammoth": "^1.10.0", "sharp": "^0.34.3", "tesseract.js": "^6.0.1"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/jest": "^30.0.0", "@types/mime-types": "^3.0.1", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.1.0", "electron-builder": "^26.0.12", "postcss": "^8.5.6", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "tsx": "^4.20.4", "typescript": "^5.8.3", "vite": "^7.0.0", "wait-on": "^8.0.3"}, "overrides": {"tmp": "^0.2.5"}}