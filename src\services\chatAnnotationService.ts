import { SmartAnnotationNote, ContextNote } from '../types/fileIntelligenceTypes'
import { unifiedAnnotationService } from './unifiedAnnotationService';
import { useSessionStore } from '../store/sessionStore';

export interface ChatAnnotationData {
  selectedText: string;
  chatMessageId: string;
  conversationId: string;
  userInput?: string;
  filePath?: string;
}

export class ChatAnnotationService {
  private static instance: ChatAnnotationService;
  private listeners: Set<(data: ChatAnnotationData) => void> = new Set();

  static getInstance(): ChatAnnotationService {
    if (!ChatAnnotationService.instance) {
      ChatAnnotationService.instance = new ChatAnnotationService();
    }
    return ChatAnnotationService.instance;
  }

  /**
   * Add selected chat content to annotations
   * If filePath is provided, adds to that file's annotations
   * If no filePath, creates a special "chat-notes" annotation
   * @param data - The chat annotation data
   */
  async addChatContentToAnnotation(data: ChatAnnotationData): Promise<boolean> {
    try {
      console.log('[CHAT-ANNOTATION] 📝 Adding chat content to annotation:', {
        selectedTextLength: data.selectedText.length,
        chatMessageId: data.chatMessageId,
        conversationId: data.conversationId,
        hasUserInput: !!data.userInput,
        filePath: data.filePath
      });

      // Get current context from session store
      const currentContext = useSessionStore.getState().getCurrentContext();
      if (!currentContext) {
        console.error('[CHAT-ANNOTATION] ❌ No current context available');
        return false;
      }

      console.log('[CHAT-ANNOTATION] 🎯 Using current context:', {
        contextId: currentContext.id,
        contextName: currentContext.name,
        vaultId: currentContext.vaultId
      });

      // Determine the target for the annotation
      const targetPath = data.filePath || `chat-notes/${data.conversationId}`;
      console.log('[CHAT-ANNOTATION] 🎯 Target path:', targetPath);
      
      // Create the annotation content
      let annotationContent = `**Chat Content:**\\n${data.selectedText}`;
      
      if (data.userInput) {
        annotationContent += `\\n\\n**User Input:**\\n${data.userInput}`;
      }
      
      annotationContent += `\\n\\n**Source:** Chat message from conversation ${data.conversationId}`;
      
      // Create the annotation note
      const annotationNote: ContextNote = {
        id: `context_note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'general_context',
        title: data.selectedText.substring(0, 100) + (data.selectedText.length > 100 ? '...' : ''),
        content: annotationContent,
        selected_text: data.selectedText,
        source_context: {
          chat_message_id: data.chatMessageId,
          conversation_id: data.conversationId,
          timestamp: new Date().toISOString(),
          user_input: data.userInput,
          vault_context: currentContext.id,
          file_path: data.filePath
        },
        metadata: {
          tags: ['chat-note', 'text-selection'],
          importance: 'medium',
          category: 'chat_insight',
          related_concepts: [],
          confidence_score: 0.8
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        note_number: 1
      };

      console.log('[CHAT-ANNOTATION] 📝 Created annotation note:', {
        noteId: annotationNote.id,
        title: annotationNote.title,
        contextId: currentContext.id
      });

      // Save using unified annotation service with current context
      const success = await unifiedAnnotationService.saveAnnotation(
        targetPath,
        annotationNote,
        currentContext.id
      );

      if (success) {
        console.log('[CHAT-ANNOTATION] ✅ Successfully saved chat annotation');
        
        // Notify listeners
        this.notifyListeners(data);
        
        return true;
      } else {
        console.error('[CHAT-ANNOTATION] ❌ Failed to save chat annotation');
        return false;
      }
    } catch (error) {
      console.error('[CHAT-ANNOTATION] 💥 Critical error in addChatContentToAnnotation:', error);
      
      // Return false instead of crashing
      return false;
    }
  }

  /**
   * Subscribe to chat annotation events
   */
  subscribe(listener: (data: ChatAnnotationData) => void): () => void {
    this.listeners.add(listener);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of new chat annotations
   */
  private notifyListeners(data: ChatAnnotationData): void {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('[CHAT-ANNOTATION] ❌ Error in listener:', error);
      }
    });
  }

  /**
   * Get all chat annotations for a specific conversation
   */
  async getChatAnnotations(conversationId: string): Promise<SmartAnnotationNote[]> {
    try {
      const targetPath = `chat-notes/${conversationId}`;
      return await unifiedAnnotationService.loadAnnotations(targetPath);
    } catch (error) {
      console.error('[CHAT-ANNOTATION] ❌ Error loading chat annotations:', error);
      return [];
    }
  }

  /**
   * Get all chat annotations across all conversations
   */
  async getAllChatAnnotations(): Promise<SmartAnnotationNote[]> {
    try {
      // This would require a more sophisticated search through all annotations
      // For now, return empty array - can be enhanced later
      return [];
    } catch (error) {
      console.error('[CHAT-ANNOTATION] ❌ Error loading all chat annotations:', error);
      return [];
    }
  }
}

export const chatAnnotationService = ChatAnnotationService.getInstance();
