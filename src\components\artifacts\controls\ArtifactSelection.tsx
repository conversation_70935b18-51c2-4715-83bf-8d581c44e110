import React, { useState } from 'react'
import { useAppStore } from '../../../store'
import { vault } from '../../../api/UnifiedAPIClient'
import { ICONS } from '../../Icons/index'

interface ArtifactSelectionProps {
  className?: string
  onArtifactsSaved?: (savedCount: number) => void
}

export function ArtifactSelection({ className = '', onArtifactsSaved }: ArtifactSelectionProps) {
  const {
    artifacts: { artifacts, currentArtifact },
    setCurrentArtifact
  } = useAppStore()

  const [selectedArtifacts, setSelectedArtifacts] = useState<Set<string>>(new Set())
  const [isSaving, setIsSaving] = useState(false)
  const [saveProgress, setSaveProgress] = useState<Map<string, { status: 'pending' | 'saving' | 'success' | 'error'; message?: string }>>(new Map())

  const getArtifactIcon = (type: string) => {
    switch (type) {
      case 'code':
      case 'json':
      case 'html':
        return 'fa-solid fa-code'
      case 'markdown':
        return 'fa-brands fa-markdown'
      case 'image':
        return 'fa-solid fa-image'
      default:
        return 'fa-solid fa-file-text'
    }
  }

  const getArtifactColor = (type: string) => {
    switch (type) {
      case 'code':
      case 'json':
      case 'html':
        return 'text-primary'
      case 'markdown':
        return 'text-secondary'
      case 'image':
        return 'text-supplement2'
      default:
        return 'text-supplement2'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  const toggleSelection = (artifactId: string) => {
    const newSelected = new Set(selectedArtifacts)
    if (newSelected.has(artifactId)) {
      newSelected.delete(artifactId)
    } else {
      newSelected.add(artifactId)
    }
    setSelectedArtifacts(newSelected)
  }

  const selectAll = () => {
    setSelectedArtifacts(new Set(artifacts.map(a => a.id)))
  }

  const clearSelection = () => {
    setSelectedArtifacts(new Set())
  }

  const getFileExtension = (type: string, content: string) => {
    switch (type) {
      case 'code':
      case 'json':
        return '.json'
      case 'html':
        return '.html'
      case 'markdown':
        return '.md'
      case 'image':
        // For image artifacts, try to detect format from content
        if (content.startsWith('data:image/')) {
          const match = content.match(/data:image\/([^;]+)/)
          return match ? `.${match[1]}` : '.png'
        }
        return '.png'
      default:
        return '.txt'
    }
  }

  const sanitizeFilename = (filename: string) => {
    return filename.replace(/[^a-zA-Z0-9._-]/g, '_')
  }

  const joinPath = (...parts: string[]) => {
    return parts.filter(Boolean).join('/').replace(/[\/\\]+/g, '/')
  }

  const getBasename = (filePath: string) => {
    const parts = filePath.split(/[\/\\]/)
    return parts[parts.length - 1] || filePath
  }

  const saveArtifactsToVault = async () => {
    if (selectedArtifacts.size === 0) {
      alert('Please select artifacts to save')
      return
    }

    setIsSaving(true)
    const selectedArtifactsList = artifacts.filter(a => selectedArtifacts.has(a.id))
    
    // Initialize progress tracking
    const progressMap = new Map()
    selectedArtifactsList.forEach(artifact => {
      progressMap.set(artifact.id, { status: 'pending' as const })
    })
    setSaveProgress(progressMap)

    let successCount = 0
    let errorCount = 0

    try {
      // Get vault registry to determine artifacts folder path
      const registry = await vault.getRegistry()
      if (!registry || !registry.vaultRoot) {
        throw new Error('Failed to get vault registry or vault root path')
      }
      const vaultRoot = registry.vaultRoot
      const artifactsFolder = joinPath(vaultRoot, 'artifacts')

      // Ensure artifacts folder exists
      try {
        const createResult = await vault.createDirectory(artifactsFolder)
        if (!createResult.success) {
          console.log('Artifacts folder creation result:', createResult)
          // Continue anyway - folder might already exist
        }
      } catch (error) {
        // Folder might already exist, continue
        console.log('Artifacts folder already exists or creation failed:', error)
      }

      // Save each selected artifact
      for (const artifact of selectedArtifactsList) {
        try {
          // Update progress to saving
          progressMap.set(artifact.id, { status: 'saving' as const })
          setSaveProgress(new Map(progressMap))

          // Generate filename
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
          const extension = getFileExtension(artifact.type, artifact.content)
          const filename = sanitizeFilename(`${artifact.title}_${timestamp}${extension}`)
          const filePath = joinPath(artifactsFolder, filename)

          // Check if file already exists
          const existsResult = await vault.pathExists(filePath)
          if (!existsResult.success) {
            throw new Error(`Failed to check if file exists: ${existsResult.error}`)
          }
          const exists = existsResult.exists
          if (exists) {
            // Add counter to filename if it exists
            let counter = 1
            let newFilePath = filePath
            while (true) {
              const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
              const ext = filename.substring(filename.lastIndexOf('.'))
              newFilePath = joinPath(artifactsFolder, `${nameWithoutExt}_${counter}${ext}`)
              
              const checkResult = await vault.pathExists(newFilePath)
              if (!checkResult.success) {
                throw new Error(`Failed to check if file exists: ${checkResult.error}`)
              }
              if (!checkResult.exists) {
                break
              }
              counter++
              
              // Safety limit to prevent infinite loops
              if (counter > 100) {
                throw new Error('Too many duplicate files, cannot generate unique filename')
              }
            }
            artifact.filePath = newFilePath
          } else {
            artifact.filePath = filePath
          }

          // Save artifact content to vault
          const writeResult = await vault.writeFile(artifact.filePath, artifact.content)
          if (!writeResult.success) {
            throw new Error(`Failed to write file: ${writeResult.error}`)
          }

          // Update progress to success
          progressMap.set(artifact.id, { 
            status: 'success' as const, 
            message: `Saved to ${artifact.filePath}` 
          })
          setSaveProgress(new Map(progressMap))
          successCount++

        } catch (error) {
          console.error(`Failed to save artifact ${artifact.id}:`, error)
          progressMap.set(artifact.id, { 
            status: 'error' as const, 
            message: `Failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
          })
          setSaveProgress(new Map(progressMap))
          errorCount++
        }
      }

      // Show results
      if (successCount > 0) {
        const message = errorCount > 0 
          ? `Successfully saved ${successCount} artifacts. ${errorCount} failed.`
          : `Successfully saved ${successCount} artifacts to vault!`
        alert(message)
        
        // Clear selection on success
        setSelectedArtifacts(new Set())
        
        // Notify parent component
        onArtifactsSaved?.(successCount)
      }

    } catch (error) {
      console.error('Failed to save artifacts to vault:', error)
      alert(`Failed to save artifacts: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSaving(false)
      // Clear progress after a delay
      setTimeout(() => setSaveProgress(new Map()), 3000)
    }
  }

  if (artifacts.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full text-gray-400 ${className}`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-gray-700/50 rounded-lg flex items-center justify-center mx-auto mb-4">
            <i className="fa-solid fa-file-text text-2xl text-gray-500"></i>
          </div>
          <div className="text-lg font-medium mb-2">No Artifacts Yet</div>
          <div className="text-sm text-gray-500">
            Artifacts will appear here when generated in your conversations
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 overflow-y-auto p-2 md:p-4 ${className}`}>
      {/* Selection and Vault Save Controls */}
      <div className="flex items-center justify-between mb-4 pb-2 border-b border-gray-600">
        <div className="flex items-center gap-2 text-sm">
          <button
            onClick={selectedArtifacts.size === artifacts.length ? clearSelection : selectAll}
            className="text-primary hover:text-primary/80 transition-colors"
            disabled={isSaving}
          >
            {selectedArtifacts.size === artifacts.length ? 'Clear' : 'Select All'}
          </button>
          {selectedArtifacts.size > 0 && (
            <span className="text-gray-400">({selectedArtifacts.size} selected)</span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {/* Save to Vault Button */}
          {selectedArtifacts.size > 0 && (
            <button
              onClick={saveArtifactsToVault}
              disabled={isSaving}
              className={`
                flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-200
                ${isSaving 
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed' 
                  : 'bg-primary text-white hover:bg-primary/80 hover:shadow-lg'
                }
              `}
              title="Save selected artifacts to vault artifacts folder"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <i className="fa-solid fa-save"></i>
                  <span>Save to Vault</span>
                </>
              )}
            </button>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {artifacts.map((artifact) => {
          const progress = saveProgress.get(artifact.id)
          const isSelected = selectedArtifacts.has(artifact.id)
          
          return (
            <div
              key={artifact.id}
              className={`
                bg-gray-700/50 rounded-lg p-4 cursor-pointer transition-all duration-200
                hover:bg-gray-700 hover:shadow-lg border-2
                ${currentArtifact?.id === artifact.id ? 'ring-2 ring-primary bg-gray-700' : ''}
                ${isSelected ? 'border-primary/50 bg-gray-700/70' : 'border-transparent'}
                ${progress?.status === 'success' ? 'border-green-500/50 bg-green-900/20' : ''}
                ${progress?.status === 'error' ? 'border-red-500/50 bg-red-900/20' : ''}
              `}
              onClick={() => setCurrentArtifact(artifact)}
            >
              {/* Artifact Header */}
              <div className="flex items-center gap-2 mb-3">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => {
                    e.stopPropagation()
                    toggleSelection(artifact.id)
                  }}
                  className="w-4 h-4 text-primary bg-gray-700 border-gray-600 rounded focus:ring-primary focus:ring-2"
                  disabled={isSaving}
                />
                <div className={`w-8 h-8 rounded-lg bg-gray-800 flex items-center justify-center ${getArtifactColor(artifact.type)}`}>
                  <i className={`${getArtifactIcon(artifact.type)} text-sm`}></i>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-supplement1 truncate">{artifact.title}</h3>
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <span className="capitalize">{artifact.type}</span>
                    <span>•</span>
                    <span>{formatTimestamp(artifact.metadata.createdAt)}</span>
                  </div>
                </div>
                
                {/* Progress Status Indicator */}
                {progress && (
                  <div className="flex items-center gap-2 text-xs">
                    {progress.status === 'pending' && (
                      <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                    )}
                    {progress.status === 'saving' && (
                      <div className="w-3 h-3 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                    )}
                    {progress.status === 'success' && (
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <i className="fa-solid fa-check text-[8px] text-white"></i>
                      </div>
                    )}
                    {progress.status === 'error' && (
                      <div className="w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                        <i className="fa-solid fa-xmark text-[8px] text-white"></i>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Content Preview */}
              <div className="mb-3">
                <div className="bg-gray-900 rounded p-3 text-sm font-mono max-h-20 overflow-hidden">
                  <div className="line-clamp-3 text-supplement2">
                    {artifact.content.substring(0, 150)}
                    {artifact.content.length > 150 && '...'}
                  </div>
                </div>
              </div>

              {/* Action Icons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {/* Render/View */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setCurrentArtifact(artifact)
                    }}
                    className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                    title="View"
                  >
                    <i className="fa-solid fa-eye"></i>
                  </button>

                  {/* Source */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      // TODO: Show source view
                    }}
                    className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                    title="Source"
                  >
                    <i className="fa-solid fa-code"></i>
                  </button>

                  {/* Copy */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      navigator.clipboard.writeText(artifact.content)
                    }}
                    className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                    title="Copy"
                  >
                    <i className="fa-solid fa-copy"></i>
                  </button>

                  {/* Download */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      const blob = new Blob([artifact.content], { type: 'text/plain' })
                      const url = URL.createObjectURL(blob)
                      const a = document.createElement('a')
                      a.href = url
                      a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt`
                      document.body.appendChild(a)
                      a.click()
                      document.body.removeChild(a)
                      URL.revokeObjectURL(url)
                    }}
                    className="p-1.5 hover:bg-gray-700 rounded transition-colors text-gray-400 text-xs"
                    title="Download"
                  >
                    <i className="fa-solid fa-download"></i>
                  </button>
                </div>

                {/* File size or line count */}
                <div className="text-xs text-gray-500">
                  {artifact.content.split('\n').length} lines
                </div>
              </div>

              {/* Progress Message */}
              {progress?.message && (
                <div className="mt-2 pt-2 border-t border-gray-600">
                  <div className={`text-xs ${
                    progress.status === 'success' ? 'text-green-400' :
                    progress.status === 'error' ? 'text-red-400' :
                    'text-gray-400'
                  }`}>
                    {progress.message}
                  </div>
                </div>
              )}

              {/* Chat Context (if available) */}
              {artifact.metadata.messageId && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <i className="fa-solid fa-message"></i>
                    <span>From conversation</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        // TODO: Navigate to message in chat
                      }}
                      className="text-primary hover:text-primary/80 underline"
                    >
                      View in chat
                    </button>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default ArtifactSelection
