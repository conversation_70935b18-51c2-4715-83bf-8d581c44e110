/**
 * Portable Event Bus Manager
 * Handles event bus reconnection and cache management across portable and local mode switches
 * 
 * WEEK 3: Event Bus & Cache Management Implementation
 */

import { cacheManager } from './cacheManager'

export interface EventSubscription {
  id: string
  category: string
  filter?: {
    pathPrefix?: string
    eventNames?: string[]
  }
  active: boolean
  lastReconnected?: Date
  reconnectAttempts: number
}

export interface CacheMigrationData {
  cacheKey: string
  data: any
  ttl: number
  portable: boolean
  lastAccessed: Date
}

export interface EventBusReconnectionResult {
  success: boolean
  reconnectedSubscriptions: number
  failedSubscriptions: number
  cacheMigrated: boolean
  error?: string
}

class PortableEventBusManager {
  private static instance: PortableEventBusManager
  private activeSubscriptions: Map<string, EventSubscription> = new Map()
  private readonly MAX_RECONNECT_ATTEMPTS = 3
  private readonly RECONNECT_DELAY_MS = 1000

  static getInstance(): PortableEventBusManager {
    if (!PortableEventBusManager.instance) {
      PortableEventBusManager.instance = new PortableEventBusManager()
    }
    return PortableEventBusManager.instance
  }

  /**
   * Register an active event subscription for portable mode management
   */
  registerSubscription(subscriptionId: string, category: string, filter?: EventSubscription['filter']): void {
    try {
      console.log('[PORTABLE-EVENT-BUS] 📝 Registering subscription:', subscriptionId, category)
      
      const subscription: EventSubscription = {
        id: subscriptionId,
        category,
        filter,
        active: true,
        reconnectAttempts: 0
      }
      
      this.activeSubscriptions.set(subscriptionId, subscription)
      console.log('[PORTABLE-EVENT-BUS] ✅ Subscription registered successfully')
    } catch (error) {
      console.error('[PORTABLE-EVENT-BUS] ❌ Failed to register subscription:', error)
    }
  }

  /**
   * Unregister an event subscription
   */
  unregisterSubscription(subscriptionId: string): void {
    try {
      console.log('[PORTABLE-EVENT-BUS] 🗑️ Unregistering subscription:', subscriptionId)
      
      if (this.activeSubscriptions.has(subscriptionId)) {
        this.activeSubscriptions.delete(subscriptionId)
        console.log('[PORTABLE-EVENT-BUS] ✅ Subscription unregistered successfully')
      } else {
        console.log('[PORTABLE-EVENT-BUS] ℹ️ Subscription not found:', subscriptionId)
      }
    } catch (error) {
      console.error('[PORTABLE-EVENT-BUS] ❌ Failed to unregister subscription:', error)
    }
  }

  /**
   * Reconnect all active event subscriptions after mode switch
   */
  async reconnectAllSubscriptions(): Promise<EventBusReconnectionResult> {
    try {
      console.log('[PORTABLE-EVENT-BUS] 🔌 Reconnecting all event subscriptions...')
      
      const activeSubscriptions = Array.from(this.activeSubscriptions.values()).filter(sub => sub.active)
      let reconnectedCount = 0
      let failedCount = 0
      
      console.log(`[PORTABLE-EVENT-BUS] 📊 Found ${activeSubscriptions.length} active subscriptions to reconnect`)
      
      for (const subscription of activeSubscriptions) {
        try {
          const success = await this.reconnectSubscription(subscription)
          if (success) {
            reconnectedCount++
            subscription.lastReconnected = new Date()
            subscription.reconnectAttempts = 0
          } else {
            failedCount++
            subscription.reconnectAttempts++
            
            // Deactivate subscription if max attempts reached
            if (subscription.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
              subscription.active = false
              console.warn(`[PORTABLE-EVENT-BUS] ⚠️ Subscription ${subscription.id} deactivated after max reconnect attempts`)
            }
          }
          
          // Add delay between reconnections to avoid overwhelming the system
          await this.delay(this.RECONNECT_DELAY_MS)
        } catch (error) {
          console.error(`[PORTABLE-EVENT-BUS] ❌ Failed to reconnect subscription ${subscription.id}:`, error)
          failedCount++
          subscription.reconnectAttempts++
        }
      }
      
      const result: EventBusReconnectionResult = {
        success: failedCount === 0,
        reconnectedSubscriptions: reconnectedCount,
        failedSubscriptions: failedCount,
        cacheMigrated: await this.migrateCacheData()
      }
      
      console.log('[PORTABLE-EVENT-BUS] ✅ Event bus reconnection completed:', result)
      return result
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      console.error('[PORTABLE-EVENT-BUS] ❌ Event bus reconnection failed:', errorMsg)
      
      return {
        success: false,
        reconnectedSubscriptions: 0,
        failedSubscriptions: this.activeSubscriptions.size,
        cacheMigrated: false,
        error: errorMsg
      }
    }
  }

  /**
   * Reconnect a specific event subscription
   */
  private async reconnectSubscription(subscription: EventSubscription): Promise<boolean> {
    try {
      console.log(`[PORTABLE-EVENT-BUS] 🔌 Reconnecting subscription: ${subscription.id} (${subscription.category})`)
      
      // Use the events API to resubscribe
      if (window.electronAPI?.invoke) {
        const result = await window.electronAPI.invoke('events:subscribe', subscription.category, subscription.filter)
        
        if (result && result.subscriptionId) {
          // Update subscription ID if it changed
          if (result.subscriptionId !== subscription.id) {
            this.activeSubscriptions.delete(subscription.id)
            subscription.id = result.subscriptionId
            this.activeSubscriptions.set(result.subscriptionId, subscription)
          }
          
          console.log(`[PORTABLE-EVENT-BUS] ✅ Successfully reconnected subscription: ${subscription.id}`)
          return true
        } else {
          console.warn(`[PORTABLE-EVENT-BUS] ⚠️ Failed to get subscription ID for: ${subscription.category}`)
          return false
        }
      } else {
        console.warn('[PORTABLE-EVENT-BUS] ⚠️ Events API not available')
        return false
      }
    } catch (error) {
      console.error(`[PORTABLE-EVENT-BUS] ❌ Failed to reconnect subscription ${subscription.id}:`, error)
      return false
    }
  }

  /**
   * Migrate cache data between portable and local modes
   */
  private async migrateCacheData(): Promise<boolean> {
    try {
      console.log('[PORTABLE-EVENT-BUS] 💾 Migrating cache data...')
      
      // Get current portable mode status
      const isPortableMode = await this.getPortableModeStatus()
      
      if (isPortableMode) {
        // Migrating to portable mode - preserve portable cache data
        return await this.preservePortableCacheData()
      } else {
        // Migrating to local mode - clear portable cache data
        return await this.clearPortableCacheData()
      }
    } catch (error) {
      console.error('[PORTABLE-EVENT-BUS] ❌ Cache migration failed:', error)
      return false
    }
  }

  /**
   * Preserve portable cache data when switching to portable mode
   */
  private async preservePortableCacheData(): Promise<boolean> {
    try {
      console.log('[PORTABLE-EVENT-BUS] 💾 Preserving portable cache data...')
      
      // Get all cache keys
      const cacheKeys = await this.getAllCacheKeys()
      let preservedCount = 0
      
      for (const key of cacheKeys) {
        try {
          // Check if this is portable data
          if (this.isPortableData(key)) {
            const data = await cacheManager.get(key)
            if (data) {
              // Mark as portable in cache
              await cacheManager.set(`portable_${key}`, {
                data,
                portable: true,
                preservedAt: new Date().toISOString()
              }, 24 * 60 * 60 * 1000) // 24 hours
              preservedCount++
            }
          }
        } catch (error) {
          console.warn(`[PORTABLE-EVENT-BUS] ⚠️ Failed to preserve cache key ${key}:`, error)
        }
      }
      
      console.log(`[PORTABLE-EVENT-BUS] ✅ Preserved ${preservedCount} portable cache entries`)
      return true
    } catch (error) {
      console.error('[PORTABLE-EVENT-BUS] ❌ Failed to preserve portable cache data:', error)
      return false
    }
  }

  /**
   * Clear portable cache data when switching to local mode
   */
  private async clearPortableCacheData(): Promise<boolean> {
    try {
      console.log('[PORTABLE-EVENT-BUS] 🧹 Clearing portable cache data...')
      
      // Get all cache keys
      const cacheKeys = await this.getAllCacheKeys()
      let clearedCount = 0
      
      for (const key of cacheKeys) {
        try {
          // Check if this is portable data
          if (this.isPortableData(key) || key.startsWith('portable_')) {
            await cacheManager.remove(key)
            clearedCount++
          }
        } catch (error) {
          console.warn(`[PORTABLE-EVENT-BUS] ⚠️ Failed to clear cache key ${key}:`, error)
        }
      }
      
      console.log(`[PORTABLE-EVENT-BUS] ✅ Cleared ${clearedCount} portable cache entries`)
      return true
    } catch (error) {
      console.error('[PORTABLE-EVENT-BUS] ❌ Failed to clear portable cache data:', error)
      return false
    }
  }

  /**
   * Get all cache keys for migration
   */
  private async getAllCacheKeys(): Promise<string[]> {
    try {
      // This is a simplified implementation
      // In a real system, you'd want to get all keys from the cache manager
      const commonKeys = [
        'vault_registry',
        'vault_scan_result',
        'vault_cards',
        'intelligence_cache',
        'file_intelligence',
        'user_preferences',
        'workflow_patterns'
      ]
      
      return commonKeys
    } catch (error) {
      console.warn('[PORTABLE-EVENT-BUS] ⚠️ Failed to get cache keys:', error)
      return []
    }
  }

  /**
   * Check if cache data is portable (should be preserved)
   */
  private isPortableData(cacheKey: string): boolean {
    const portableKeys = [
      'vault_registry',
      'vault_scan_result',
      'vault_cards',
      'intelligence_cache',
      'file_intelligence',
      'user_preferences',
      'workflow_patterns'
    ]
    
    return portableKeys.some(key => cacheKey.includes(key))
  }

  /**
   * Get current portable mode status
   */
  private async getPortableModeStatus(): Promise<boolean> {
    try {
      if (window.electronAPI?.settings) {
        const status = await window.electronAPI.settings.get('portable-mode-enabled')
        return status === true || status === 'true'
      }
      return false
    } catch (error) {
      console.warn('[PORTABLE-EVENT-BUS] Failed to get portable mode status:', error)
      return false
    }
  }

  /**
   * Get active subscriptions count
   */
  getActiveSubscriptionsCount(): number {
    return Array.from(this.activeSubscriptions.values()).filter(sub => sub.active).length
  }

  /**
   * Get subscription statistics
   */
  getSubscriptionStats(): {
    total: number
    active: number
    inactive: number
    categories: Record<string, number>
  } {
    const subscriptions = Array.from(this.activeSubscriptions.values())
    const active = subscriptions.filter(sub => sub.active)
    const inactive = subscriptions.filter(sub => !sub.active)
    
    const categories: Record<string, number> = {}
    subscriptions.forEach(sub => {
      categories[sub.category] = (categories[sub.category] || 0) + 1
    })
    
    return {
      total: subscriptions.length,
      active: active.length,
      inactive: inactive.length,
      categories
    }
  }

  /**
   * Clear all subscriptions (for cleanup)
   */
  clearAllSubscriptions(): void {
    console.log('[PORTABLE-EVENT-BUS] 🧹 Clearing all subscriptions...')
    this.activeSubscriptions.clear()
    console.log('[PORTABLE-EVENT-BUS] ✅ All subscriptions cleared')
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const portableEventBusManager = PortableEventBusManager.getInstance()

// Export default for direct import
export default portableEventBusManager
