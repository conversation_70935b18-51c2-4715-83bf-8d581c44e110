FilesPage.tsx:147 [FILES] 🚀 Starting FilesPage initialization sequence...
FilesPage.tsx:152 [FILES] Step 1: Initializing path service...
FilesPage.tsx:529 [FILES] Loading file tree for context: undefined
FilesPage.tsx:156 [FILES] ✅ Path service initialized
FilesPage.tsx:162 [FILES] Step 2: Loading file tree...
FilesPage.tsx:529 [FILES] Loading file tree for context: undefined
FilesPage.tsx:539 [FILES] Raw file tree from vaultUIManager: 3 items
FilesPage.tsx:539 [FILES] Raw file tree from vaultUIManager: 3 items
FilesPage.tsx:164 [FILES] ✅ File tree loaded
FilesPage.tsx:166 [FILES] 🎉 FilesPage initialization completed successfully
FilesPage.tsx:170 [FILES] 🔍 Running vault loading diagnostics...
FilesPage.tsx:175 [FILES] 🚨 Vault loading issues detected: (2) ['3 operations failed', '1 operations were slow (>5s)']0: "3 operations failed"1: "1 operations were slow (>5s)"length: 2[[Prototype]]: Array(0)
initializeFilesPage @ FilesPage.tsx:175
await in initializeFilesPage
(anonymous) @ FilesPage.tsx:192
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17486
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
commitHookEffectListMount @ react-dom_client.js?v=58b462a4:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=58b462a4:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=58b462a4:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=58b462a4:9899
flushPassiveEffects @ react-dom_client.js?v=58b462a4:11302
(anonymous) @ react-dom_client.js?v=58b462a4:11060
performWorkUntilDeadline @ react-dom_client.js?v=58b462a4:36
<FilesPage>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=58b462a4:250
App @ App.tsx:214
react_stack_bottom_frame @ react-dom_client.js?v=58b462a4:17424
renderWithHooks @ react-dom_client.js?v=58b462a4:4206
updateFunctionComponent @ react-dom_client.js?v=58b462a4:6619
beginWork @ react-dom_client.js?v=58b462a4:7654
runWithFiberInDEV @ react-dom_client.js?v=58b462a4:1485
performUnitOfWork @ react-dom_client.js?v=58b462a4:10868
workLoopSync @ react-dom_client.js?v=58b462a4:10728
renderRootSync @ react-dom_client.js?v=58b462a4:10711
performWorkOnRoot @ react-dom_client.js?v=58b462a4:10330
performSyncWorkOnRoot @ react-dom_client.js?v=58b462a4:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=58b462a4:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=58b462a4:11558
(anonymous) @ react-dom_client.js?v=58b462a4:11649
FilesPage.tsx:176 [FILES] 💡 Recommendations: (2) ['Check error logs and implement proper error handling', 'Optimize slow operations and implement caching']0: "Check error logs and implement proper error handling"1: "Optimize slow operations and implement caching"length: 2[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: Object


UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: Object
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: Object
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 3, args: Array(3), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:writeFile', category: 'vault', endpoint: 'writeFile', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:writeFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 3, args: Array(3), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:writeFile', category: 'vault', endpoint: 'writeFile', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:writeFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 3, args: Array(3), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:writeFile', category: 'vault', endpoint: 'writeFile', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:writeFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 3, args: Array(3), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:writeFile', category: 'vault', endpoint: 'writeFile', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:writeFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readDirectory', category: 'vault', endpoint: 'readDirectory', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readDirectory', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'path:join', category: 'path', endpoint: 'join', argsCount: 2, args: Array(2), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'path:join', success: false, hasData: false, hasError: true, responseType: 'object', …}
UnifiedAPIClient.ts:103 [IPC-LOG] ❌ API Error Response: {channelName: 'path:join', error: 'this.pathResolver.joinPaths is not a function', code: undefined}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:pathExists', category: 'vault', endpoint: 'pathExists', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:pathExists', success: true, hasData: false, hasError: false, responseType: 'object', …}
UnifiedAPIClient.ts:70 [IPC-LOG] 📡 API Call: {channelName: 'vault:readFile', category: 'vault', endpoint: 'readFile', argsCount: 1, args: Array(1), …}
UnifiedAPIClient.ts:91 [IPC-LOG] ✅ API Response: {channelName: 'vault:readFile', success: true, hasData: false, hasError: false, responseType: 'object', …}
