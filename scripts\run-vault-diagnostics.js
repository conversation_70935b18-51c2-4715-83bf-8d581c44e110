#!/usr/bin/env node

/**
 * Vault Loading Diagnostics Runner
 * 
 * This script runs the vault loading diagnostics to identify issues
 * with the loading sequence, state management, and path resolution.
 * 
 * Usage:
 *   node scripts/run-vault-diagnostics.js [options]
 * 
 * Options:
 *   --test          Run the test suite
 *   --monitor       Start real-time monitoring
 *   --quick         Run quick diagnostics
 *   --context-id    Specify context ID for testing
 */

const { spawn } = require('child_process')
const path = require('path')

// Parse command line arguments
const args = process.argv.slice(2)
const options = {
  test: args.includes('--test'),
  monitor: args.includes('--monitor'),
  quick: args.includes('--quick'),
  contextId: args.includes('--context-id') ? args[args.indexOf('--context-id') + 1] : undefined
}

console.log('🔍 [DIAGNOSTICS] Vault Loading Diagnostics Runner')
console.log('🔍 [DIAGNOSTICS] Options:', options)

async function runTests() {
  console.log('🔍 [DIAGNOSTICS] Running test suite...')
  
  return new Promise((resolve, reject) => {
    const testProcess = spawn('npm', ['run', 'test', 'tests/vault-loading-diagnostics.test.ts'], {
      cwd: process.cwd(),
      stdio: 'inherit'
    })

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('🔍 [DIAGNOSTICS] ✅ Test suite completed successfully')
        resolve()
      } else {
        console.log('🔍 [DIAGNOSTICS] ❌ Test suite failed with code:', code)
        reject(new Error(`Test suite failed with code ${code}`))
      }
    })

    testProcess.on('error', (error) => {
      console.error('🔍 [DIAGNOSTICS] ❌ Failed to run test suite:', error)
      reject(error)
    })
  })
}

async function runQuickDiagnostics() {
  console.log('🔍 [DIAGNOSTICS] Running quick diagnostics...')
  
  // This would need to be integrated with the actual app
  // For now, we'll provide instructions
  console.log('🔍 [DIAGNOSTICS] To run quick diagnostics in your app:')
  console.log('🔍 [DIAGNOSTICS] 1. Import: import { runQuickDiagnostics } from "./src/utils/vaultLoadingDiagnostics"')
  console.log('🔍 [DIAGNOSTICS] 2. Call: await runQuickDiagnostics(contextId)')
  console.log('🔍 [DIAGNOSTICS] 3. Check console for detailed report')
  
  if (options.contextId) {
    console.log(`🔍 [DIAGNOSTICS] Context ID: ${options.contextId}`)
  }
}

async function startMonitoring() {
  console.log('🔍 [DIAGNOSTICS] Starting real-time monitoring...')
  
  // This would need to be integrated with the actual app
  console.log('🔍 [DIAGNOSTICS] To start monitoring in your app:')
  console.log('🔍 [DIAGNOSTICS] 1. Import: import { startLoadingMonitor } from "./src/utils/vaultLoadingDiagnostics"')
  console.log('🔍 [DIAGNOSTICS] 2. Call: await startLoadingMonitor(1000, 30000) // 1s interval, 30s duration')
  console.log('🔍 [DIAGNOSTICS] 3. Monitor console for real-time updates')
}

async function generateIntegrationCode() {
  console.log('🔍 [DIAGNOSTICS] === INTEGRATION CODE ===')
  console.log('')
  console.log('// Add this to your FilesPage.tsx for debugging:')
  console.log('')
  console.log('import { runQuickDiagnostics, startLoadingMonitor } from "../utils/vaultLoadingDiagnostics"')
  console.log('')
  console.log('// In your component:')
  console.log('useEffect(() => {')
  console.log('  const runDiagnostics = async () => {')
  console.log('    if (process.env.NODE_ENV === "development") {')
  console.log('      console.log("🔍 Running vault loading diagnostics...")')
  console.log('      const report = await runQuickDiagnostics(selectedContextId)')
  console.log('      ')
  console.log('      if (report.issues.length > 0) {')
  console.log('        console.error("🚨 Vault loading issues detected:", report.issues)')
  console.log('        console.log("💡 Recommendations:", report.recommendations)')
  console.log('      }')
  console.log('    }')
  console.log('  }')
  console.log('  ')
  console.log('  runDiagnostics().catch(console.error)')
  console.log('}, [selectedContextId])')
  console.log('')
  console.log('// For continuous monitoring, add this button to your UI:')
  console.log('<button onClick={() => startLoadingMonitor(1000, 30000)}>')
  console.log('  Start Vault Monitoring')
  console.log('</button>')
  console.log('')
}

async function main() {
  try {
    if (options.test) {
      await runTests()
    }
    
    if (options.quick) {
      await runQuickDiagnostics()
    }
    
    if (options.monitor) {
      await startMonitoring()
    }
    
    if (!options.test && !options.quick && !options.monitor) {
      console.log('🔍 [DIAGNOSTICS] No specific option provided, showing integration code...')
      await generateIntegrationCode()
    }
    
    console.log('🔍 [DIAGNOSTICS] === SUMMARY ===')
    console.log('🔍 [DIAGNOSTICS] The diagnostic tools are now available:')
    console.log('🔍 [DIAGNOSTICS] 1. Test Suite: tests/vault-loading-diagnostics.test.ts')
    console.log('🔍 [DIAGNOSTICS] 2. Runtime Diagnostics: src/utils/vaultLoadingDiagnostics.ts')
    console.log('🔍 [DIAGNOSTICS] 3. Integration: Use the code above in your components')
    console.log('')
    console.log('🔍 [DIAGNOSTICS] Next steps:')
    console.log('🔍 [DIAGNOSTICS] 1. Run: npm test tests/vault-loading-diagnostics.test.ts')
    console.log('🔍 [DIAGNOSTICS] 2. Integrate diagnostics into FilesPage.tsx')
    console.log('🔍 [DIAGNOSTICS] 3. Monitor console output during vault loading')
    console.log('🔍 [DIAGNOSTICS] 4. Fix identified issues based on diagnostic reports')
    
  } catch (error) {
    console.error('🔍 [DIAGNOSTICS] ❌ Error running diagnostics:', error)
    process.exit(1)
  }
}

// Run the diagnostics
main().catch(console.error)
