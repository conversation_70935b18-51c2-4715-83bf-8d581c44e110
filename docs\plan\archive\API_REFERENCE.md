# ChatLo Unified IPC System - API Reference

## Table of Contents
1. [Core API Endpoints](#core-api-endpoints)
   - [Database APIs (`db`)](#database-apis-db-category)
   - [Vault APIs (`vault`)](#vault-apis-vault-category)
   - [Files APIs (`files`)](#files-apis-files-category)
   - [Intelligence APIs (`intelligence`)](#intelligence-apis-intelligence-category)
   - [Events APIs (`events`)](#events-apis-events-category)
   - [Settings APIs (`settings`)](#settings-apis-settings-category)
   - [Shell APIs (`shell`)](#shell-apis-shell-category)
   - [Plugin APIs (`plugins`)](#plugin-apis-plugins-category)
2. [Plugin API System](#plugin-api-system)
3. [Frontend API Client](#frontend-api-client)
4. [React Hooks](#react-hooks)
5. [Error Handling](#error-handling)
6. [Middleware System](#middleware-system)
7. [Monitoring & Metrics](#monitoring--metrics)
8. [Security & Validation](#security--validation)
9. [Window Controls](#window-controls)

## Core API Endpoints

### Database APIs (`db` category)

#### `db:getConversations`
Retrieve all conversations from the database.

**Parameters:** None

**Response:**
```typescript
Conversation[]
```

#### `db:getConversation`
Get a specific conversation by ID.

**Parameters:**
- `id: string` - Conversation ID (max 100 chars)

**Response:**
```typescript
Conversation | null
```

#### `db:createConversation`
Create a new conversation.

**Parameters:**
- `title: string` - Conversation title (max 200 chars)

**Response:**
```typescript
string // New conversation ID
```

#### `db:updateConversation`
Update conversation title.

**Parameters:**
- `id: string` - Conversation ID (max 100 chars)
- `title: string` - New title (max 200 chars)

**Response:**
```typescript
void
```

#### `db:deleteConversation`
Delete a conversation and all its messages.

**Parameters:**
- `id: string` - Conversation ID (max 100 chars)

**Response:**
```typescript
void
```

#### `db:getMessages`
Get all messages for a conversation.

**Parameters:**
- `conversationId: string` - Conversation ID (max 100 chars)

**Response:**
```typescript
Message[]
```

#### `db:addMessage`
Add a new message to a conversation.

**Parameters:**
- `conversationId: string` - Conversation ID (max 100 chars)
- `message: Omit<Message, 'id' | 'created_at'>` - Message object

**Response:**
```typescript
string // New message ID
```

#### `db:togglePinMessage`
Toggle pin status of a message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)

**Response:**
```typescript
void
```

#### `db:updateMessageIntelligence`
Update intelligence data for a message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)
- `entities: string` - Extracted entities (max 10000 chars)
- `topics: string` - Identified topics (max 5000 chars)
- `confidence: number` - Confidence score (0-1)

**Response:**
```typescript
void
```

#### `db:addPinnedIntelligence`
Add pinned intelligence data.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)
- `extractionData: string` - Extraction data (max 50000 chars)
- `vaultAssignment: string` - Vault assignment (max 1000 chars)
- `processingMetadata: string` - Processing metadata (max 10000 chars)

**Response:**
```typescript
string // New pinned intelligence ID
```

#### `db:getPinnedIntelligence`
Get pinned intelligence for a message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)

**Response:**
```typescript
any // Pinned intelligence data
```

#### `db:getAllPinnedIntelligence`
Get all pinned intelligence data.

**Parameters:** None

**Response:**
```typescript
any[]
```

#### `db:searchConversations`
Search conversations by term.

**Parameters:**
- `searchTerm: string` - Search query (max 200 chars)

**Response:**
```typescript
any[]
```

#### `db:getConversationsWithArtifacts`
Get conversations that have artifacts.

**Parameters:** None

**Response:**
```typescript
any[]
```

#### `db:getArtifacts`
Get artifacts for a message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)

**Response:**
```typescript
any[]
```

#### `db:addArtifact`
Add an artifact to a message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)
- `artifact: any` - Artifact object

**Response:**
```typescript
string // New artifact ID
```

#### `db:updateArtifact`
Update an artifact.

**Parameters:**
- `id: string` - Artifact ID (max 100 chars)
- `updates: any` - Update object

**Response:**
```typescript
void
```

#### `db:removeArtifact`
Remove an artifact.

**Parameters:**
- `id: string` - Artifact ID (max 100 chars)

**Response:**
```typescript
void
```

#### `db:getConversationArtifacts`
Get all artifacts for a conversation.

**Parameters:**
- `conversationId: string` - Conversation ID (max 100 chars)

**Response:**
```typescript
any[]
```

#### `db:getDatabaseHealth`
Get database health status.

**Parameters:** None

**Response:**
```typescript
any // Health status object
```

#### `db:createBackup`
Create a database backup.

**Parameters:** None

**Response:**
```typescript
string // Backup file path
```

### Vault APIs (`vault` category)

#### `vault:createDirectory`
Create directory recursively.

**Parameters:**
- `dirPath: string` - Directory path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:writeFile`
Write content to file with base64 support.

**Parameters:**
- `filePath: string` - File path (max 500 chars)
- `content: string` - File content (max 200MB)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:appendFile`
Append content to file with base64 support.

**Parameters:**
- `filePath: string` - File path (max 500 chars)
- `content: string` - Content to append (max 200MB)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:readDirectory`
Read directory contents.

**Parameters:**
- `dirPath: string` - Directory path (max 500 chars)

**Response:**
```typescript
{
  success: boolean;
  error?: string;
  items: Array<{
    name: string;
    path: string;
    isDirectory: boolean;
    size?: number;
    modified: string;
  }>
}
```

#### `vault:removeDirectory`
Remove directory recursively.

**Parameters:**
- `dirPath: string` - Directory path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:removeFile`
Remove file.

**Parameters:**
- `filePath: string` - File path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:scanFolder`
Scan folder for files.

**Parameters:**
- `folderPath: string` - Folder path (max 500 chars)

**Response:**
```typescript
{
  success: boolean;
  error?: string;
  files: Array<{
    name: string;
    path: string;
    isDirectory: boolean;
    size: number;
    lastModified: string;
  }>
}
```

#### `vault:copyFile`
Copy file from source to destination.

**Parameters:**
- `sourcePath: string` - Source file path (max 500 chars)
- `destinationPath: string` - Destination path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:pathExists`
Check if path exists.

**Parameters:**
- `targetPath: string` - Path to check (max 500 chars)

**Response:**
```typescript
{ exists: boolean; error?: string }
```

#### `vault:readFile`
Read file content.

**Parameters:**
- `filePath: string` - File path (max 500 chars)

**Response:**
```typescript
{ success: boolean; content?: string; error?: string }
```

#### `vault:getVaultRegistry`
Get vault registry configuration.

**Parameters:** None

**Response:**
```typescript
any // Vault registry object or null
```

#### `vault:saveVaultRegistry`
Save vault registry configuration.

**Parameters:**
- `registry: any` - Registry object

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `vault:initializeVaultRoot`
Initialize vault root with template.

**Parameters:**
- `rootPath: string` - Root path (max 500 chars)
- `template: string` - Template type (max 50 chars)

**Response:**
```typescript
{
  success: boolean;
  vaults: any[];
  error?: string;
}
```

#### `vault:scanContexts`
Scan vault for contexts.

**Parameters:**
- `vaultPath: string` - Vault path (max 500 chars)

**Response:**
```typescript
{
  success: boolean;
  contexts: Array<{
    id: string;
    name: string;
    path: string;
    hasMaster: boolean;
  }>;
  error?: string;
}
```

### Files APIs (`files` category)

#### `files:getChatloFolderPath`
Get the current ChatLo folder path.

**Parameters:** None

**Response:**
```typescript
string // Current folder path
```

#### `files:setChatloFolderPath`
Set a new ChatLo folder path.

**Parameters:**
- `newPath: string` - New folder path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `files:getIndexedFiles`
Get all indexed files.

**Parameters:** None

**Response:**
```typescript
any[] // Array of indexed files
```

#### `files:searchFiles`
Search files by query.

**Parameters:**
- `query: string` - Search query (max 200 chars)
- `limit?: number` - Result limit (1-1000, optional)

**Response:**
```typescript
any[] // Search results
```

#### `files:processFileContent`
Process file content for intelligence.

**Parameters:**
- `fileId: string` - File ID (max 100 chars)

**Response:**
```typescript
boolean // Success status
```

#### `files:indexFile`
Index a single file.

**Parameters:**
- `filePath: string` - File path (max 500 chars)
- `processContent?: boolean` - Whether to process content (optional)

**Response:**
```typescript
any // Index result or null
```

#### `files:indexVaultFile`
Index a vault file with vault-specific metadata.

**Parameters:**
- `filePath: string` - File path (max 500 chars)
- `vaultName: string` - Vault name (max 100 chars)
- `relativePath: string` - Relative path (max 500 chars)
- `processContent?: boolean` - Whether to process content (optional)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `files:indexAllFiles`
Index all files in ChatLo folder.

**Parameters:** None

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `files:copyFileToUploads`
Copy file to uploads directory.

**Parameters:**
- `sourcePath: string` - Source path (max 500 chars)
- `filename?: string` - Target filename (max 255 chars, optional)

**Response:**
```typescript
string // Destination path
```

#### `files:saveContentAsFile`
Save content as a file.

**Parameters:**
- `content: string` - File content (max 10MB)
- `filename: string` - Filename (max 255 chars)
- `subfolder?: string` - Subfolder (max 100 chars, optional)

**Response:**
```typescript
string // File path
```

#### `files:addFileAttachment`
Add file attachment to message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)
- `fileId: string` - File ID (max 100 chars)
- `attachmentType: 'attachment' | 'reference'` - Attachment type

**Response:**
```typescript
string // Attachment ID
```

#### `files:getFileAttachments`
Get file attachments for message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)

**Response:**
```typescript
any[] // File attachments
```

#### `files:getMessageFiles`
Get files associated with message.

**Parameters:**
- `messageId: string` - Message ID (max 100 chars)

**Response:**
```typescript
any[] // Associated files
```

#### `files:removeFileAttachment`
Remove file attachment.

**Parameters:**
- `attachmentId: string` - Attachment ID (max 100 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `files:deleteFile`
Delete a file.

**Parameters:**
- `fileId: string` - File ID (max 100 chars)

**Response:**
```typescript
boolean // Success status
```

#### `files:showOpenDialog`
Show open file dialog.

**Parameters:**
- `options: any` - Dialog options object

**Response:**
```typescript
any // Dialog result
```

#### `files:showSaveDialog`
Show save file dialog.

**Parameters:**
- `options: any` - Dialog options object

**Response:**
```typescript
any // Dialog result
```

#### `files:getFileContent`
Get file content by path.

**Parameters:**
- `filePath: string` - File path (max 500 chars)

**Response:**
```typescript
string | null // File content
```

#### `files:fileExists`
Check if file exists.

**Parameters:**
- `filePath: string` - File path (max 500 chars)

**Response:**
```typescript
boolean // Exists status
```

#### `files:processFile`
Process file with appropriate plugin.

**Parameters:**
- `filePath: string` - File path (max 500 chars)
- `fileType?: string` - File type hint (optional)

**Response:**
```typescript
{
  success: boolean;
  content?: {
    text: string;
    metadata?: any; // Plugin-specific metadata, e.g., markdown frontmatter, counts, PDF info
  };
  error?: string;
}
```

Notes:
- Dispatches to Electron File Processor Plugins defined under `electron/fileProcessors/*`.
- For markdown: `MarkdownPlugin` extracts YAML frontmatter (best-effort) and basic metrics (headers, lists, code blocks, etc.).
- For pdf/image: plugins may perform parsing/OCR and attach metadata.

#### `files:openPDFViewer`
Open PDF viewer for file.

**Parameters:**
- `filePath: string` - PDF file path (max 500 chars)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `files:getFileUrl`
Get file URL for display.

**Parameters:**
- `filePath: string` - File path (max 500 chars)

**Response:**
```typescript
string // File URL
```

### Intelligence APIs (`intelligence` category)

> Persistence and parsing of intelligence data for files. Storage target is `<vault>/.intelligence/files/<hash>.json` for single-file JSON; sessionized storage, if used, lives under `<vault>/.intelligence/documents/<hash>/sessions/session_<ts>.json`.

#### `intelligence:read`
Read intelligence for a file.

**Parameters:**
- `filePath: string` - Absolute path to the file (max 500 chars)

**Response:**
```typescript
{ success: boolean; data?: IntelligenceData; error?: string }
```

#### `intelligence:write`
Write intelligence for a file (creates directories if needed).

**Parameters:**
- `filePath: string` - Absolute path to the file (max 500 chars)
- `data: IntelligenceData` - Intelligence payload (validated)

**Response:**
```typescript
{ success: boolean; error?: string }
```

#### `intelligence:listSessions`
List available intelligence sessions for a file (if sessionized storage is configured).

**Parameters:**
- `filePath: string` - Absolute path to the file (max 500 chars)

**Response:**
```typescript
{ success: boolean; sessions?: Array<{ id: string; timestamp: string; path: string }>; error?: string }
```

#### `intelligence:parseMarkdown`
Parse LLM Markdown output into IntelligenceData using a single, strict parser.

**Parameters:**
- `markdown: string` - Markdown content (max 1MB)

**Response:**
```typescript
{ success: boolean; data?: IntelligenceData; error?: string }
```

### Events APIs (`events` category)

> Subscribe to realtime events from the main process. Categories include `file`, `intelligence`, and `task`.

#### `events:subscribe`
Subscribe to event stream.

**Parameters:**
- `category: 'file' | 'intelligence' | 'task'`
- `filter?: any` - Optional filter object

**Response:**
```typescript
{ subscriptionId: string }
```

#### `events:unsubscribe`
Unsubscribe from event stream.

**Parameters:**
- `subscriptionId: string` - ID returned by `events:subscribe`

**Response:**
```typescript
{ success: boolean }
```

#### Event Payloads (main → renderer)
- `file:added|changed|removed`
```typescript
{ path: string; vaultId?: string; isDirectory?: boolean; size?: number; modified?: string }
```
- `intelligence:updated`
```typescript
{ filePath: string; vaultId?: string; ideaCount?: number; updatedAt: string }
```
- `task:progress`
```typescript
{ taskId: string; percent: number; message?: string }
```

### Settings APIs (`settings` category)

#### `settings:get`
Get a setting value.

**Parameters:**
- `key: string` - Setting key (max 100 chars)

**Response:**
```typescript
any // Setting value
```

#### `settings:set`
Set a setting value.

**Parameters:**
- `key: string` - Setting key (max 100 chars)
- `value: any` - Setting value

**Response:**
```typescript
void
```

### Shell APIs (`shell` category)

#### `shell:openPath`
Open path in system default application.

**Parameters:**
- `path: string` - File or directory path

**Response:**
```typescript
string // Result status
```

#### `shell:showItemInFolder`
Show item in system file explorer.

**Parameters:**
- `path: string` - File or directory path

**Response:**
```typescript
void
```

### Plugin APIs (`plugins` category)

#### `plugins:getAll`
Get all available plugins.

**Parameters:** None

**Response:**
```typescript
any[] // Array of plugin objects
```

#### `plugins:enable`
Enable a plugin.

**Parameters:**
- `pluginId: string` - Plugin ID
- `enabled: boolean` - Enable status

**Response:**
```typescript
void
```

#### `plugins:disable`
Disable a plugin.

**Parameters:**
- `pluginId: string` - Plugin ID

**Response:**
```typescript
void
```

#### `plugins:discover`
Discover available plugins.

**Parameters:** None

**Response:**
```typescript
void
```

#### `plugins:getConfig`
Get plugin configuration.

**Parameters:**
- `pluginId: string` - Plugin ID

**Response:**
```typescript
any // Plugin configuration
```

#### `plugins:updateConfig`
Update plugin configuration.

**Parameters:**
- `pluginId: string` - Plugin ID
- `config: any` - New configuration

**Response:**
```typescript
void
```

#### `plugins:getCapabilities`
Get plugin capabilities.

**Parameters:** None

**Response:**
```typescript
any // Capabilities object
```

#### `plugins:getAPIEndpoints`
Get API endpoints for a specific plugin.

**Parameters:**
- `pluginId: string` - Plugin ID

**Response:**
```typescript
{
  success: boolean;
  namespace?: string;
  endpoints?: any[];
  error?: string;
}
```

#### `plugins:getAllAPIEndpoints`
Get all plugin API endpoints.

**Parameters:** None

**Response:**
```typescript
{
  success: boolean;
  apiInfo?: any[];
  error?: string;
}
```

## Window Controls

### Window Management
ChatLo provides window control methods for managing the application window:

#### `windowControls.minimize()`
Minimize the application window.

**Parameters:** None

**Response:** None

#### `windowControls.maximize()`
Maximize/restore the application window.

**Parameters:** None

**Response:** None

#### `windowControls.close()`
Close the application window.

**Parameters:** None

**Response:** None

#### `windowControls.aspect16_9()`
Set window to 16:9 aspect ratio.

**Parameters:** None

**Response:** None

#### `windowControls.aspect10_16()`
Set window to 10:16 aspect ratio.

**Parameters:** None

**Response:** None

**Usage:**
```typescript
// Minimize window
window.electronAPI.windowControls.minimize()

// Maximize window
window.electronAPI.windowControls.maximize()

// Close window
window.electronAPI.windowControls.close()

// Set aspect ratios
window.electronAPI.windowControls.aspect16_9()
window.electronAPI.windowControls.aspect10_16()

## Plugin API System

### APIRegistry Architecture
The ChatLo plugin system is built on a unified APIRegistry that provides:

- **Category-based organization**: APIs are grouped by functionality (db, vault, files, etc.)
- **Middleware support**: Request/response processing and validation
- **Type safety**: Full TypeScript support with parameter validation
- **Error handling**: Structured error responses with context
- **Performance monitoring**: Built-in metrics and health checks

### Plugin Registration
Plugins can register new API endpoints using the APIRegistry:

```typescript
// Register a new category
apiRegistry.registerCategory('myPlugin')

// Register an endpoint
apiRegistry.registerEndpoint('myPlugin', 'myMethod',
  async (param1: string, param2: number) => {
    // Implementation
    return { success: true, data: result }
  },
  {
    validator: (param1: string, param2: number) => {
      if (!param1) throw new Error('param1 is required')
      if (param2 < 0) throw new Error('param2 must be positive')
    },
    description: 'My custom method'
  }
)
```

### Plugin Discovery
### Renderer File Type Plugin System (Note)

The renderer handles display via a separate plugin system under `src/components/FileTypeRenderer/*`.

- Registry: `src/components/FileTypeRenderer/index.tsx`
- Types:
  ```typescript
  interface FileTypeInfo { type: 'pdf'|'markdown'|'mermaid'|'text'|'image'|'code'|'unsupported'; /* ... */ }
  interface FileTypePlugin {
    canHandle(fileTypeInfo: FileTypeInfo): boolean;
    render(props: FileRenderProps): React.ReactNode;
    extractText?(content: string): Promise<string>;
    getMetadata?(content: string): Promise<any>;
  }
  ```

Important: Intelligence extraction should prefer Electron `files:processFile` for types with processors (pdf/image/markdown). The page (`FilePageOverlay.tsx`) consolidates extracted text and plugin metadata via `onContentExtracted` into the unified intelligence state.

The system automatically discovers and loads plugins from the plugins directory:

```typescript
// Get all available plugins
const plugins = await window.electronAPI.plugins.getAll()

// Get plugin capabilities
const capabilities = await window.electronAPI.plugins.getCapabilities()

// Get plugin API endpoints
const endpoints = await window.electronAPI.plugins.getAPIEndpoints('myPlugin')
```

## Frontend API Client

### Usage
The frontend can access all APIs through the global `electronAPI` object:

```typescript
// Database operations
const conversations = await window.electronAPI.db.getConversations()
const newConv = await window.electronAPI.db.createConversation('My Chat')

// Vault operations
const fileContent = await window.electronAPI.vault.readFile('/path/to/file.txt')
await window.electronAPI.vault.writeFile('/path/to/new.txt', 'content')

// File operations
const files = await window.electronAPI.files.getIndexedFiles()
await window.electronAPI.files.indexFile('/path/to/document.pdf')

// Settings
const setting = await window.electronAPI.settings.get('theme')
await window.electronAPI.settings.set('theme', 'dark')

// Shell operations
window.electronAPI.shell.openPath('/path/to/file')
window.electronAPI.shell.showItemInFolder('/path/to/file')

// Plugin operations
const plugins = await window.electronAPI.plugins.getAll()
await window.electronAPI.plugins.enable('myPlugin', true)
```

## React Hooks

### useElectronAPI
Custom hook for accessing Electron APIs in React components:

```typescript
import { useElectronAPI } from '../hooks/useElectronAPI'

function MyComponent() {
  const { db, vault, files } = useElectronAPI()

  const loadConversations = async () => {
    const conversations = await db.getConversations()
    // Handle conversations
  }

  return (
    <button onClick={loadConversations}>
      Load Conversations
    </button>
  )
}
```

### useVault
Hook for vault operations:

```typescript
import { useVault } from '../hooks/useVault'

function VaultComponent() {
  const { readFile, writeFile, createDirectory } = useVault()

  const handleFileRead = async (path: string) => {
    const content = await readFile(path)
    return content
  }

  return <div>Vault operations</div>
}

## Error Handling

### Structured Error Responses
All API calls return structured error responses when failures occur:

```typescript
{
  success: false,
  error: string,
  code?: string,
  details?: any
}
```

### Error Types
- **ValidationError**: Invalid parameters or input data
- **NotFoundError**: Requested resource not found
- **PermissionError**: Insufficient permissions
- **SystemError**: System-level errors (file system, database, etc.)
- **PluginError**: Plugin-specific errors

### Error Handling Best Practices

```typescript
try {
  const result = await window.electronAPI.vault.readFile('/path/to/file.txt')
  if (result.success) {
    console.log('File content:', result.content)
  } else {
    console.error('Failed to read file:', result.error)
  }
} catch (error) {
  console.error('API call failed:', error)
}
```

## Middleware System

### Request Middleware
The APIRegistry supports middleware for request processing:

```typescript
// Add validation middleware
apiRegistry.addMiddleware('request', (category, method, args) => {
  console.log(`API call: ${category}:${method}`, args)
  // Perform validation, logging, etc.
  return args // Return modified args if needed
})
```

### Response Middleware
Response middleware for post-processing:

```typescript
// Add response logging
apiRegistry.addMiddleware('response', (category, method, result) => {
  console.log(`API response: ${category}:${method}`, result)
  return result
})
```

## Monitoring & Metrics

### Performance Tracking
The system includes built-in performance monitoring:

```typescript
// Get API performance metrics
const metrics = await window.electronAPI.system.getMetrics()

// Monitor specific API calls
const startTime = performance.now()
await window.electronAPI.db.getConversations()
const duration = performance.now() - startTime
console.log(`API call took ${duration}ms`)
```

### Health Checks
Regular health checks ensure system stability:

```typescript
// Check database health
const dbHealth = await window.electronAPI.db.getDatabaseHealth()

// Check plugin health
const pluginHealth = await window.electronAPI.plugins.getCapabilities()
```
## Security & Validation

### Input Validation
All API endpoints include comprehensive input validation:

```typescript
// String validation with length limits
if (!this.validateInput(filePath, 'string', 500)) {
  throw new Error('Invalid file path')
}

// Number validation with range checks
if (!Number.isInteger(limit) || limit < 1 || limit > 1000) {
  throw new Error('Invalid limit value')
}

// Object validation
if (!registry || typeof registry !== 'object') {
  throw new Error('Invalid registry object')
}
```

### Parameter Limits
- **String parameters**: Maximum length varies by use case (50-500 chars for paths, 100-200 for IDs)
- **File content**: Maximum 200MB for file operations, 10MB for text content
- **Search queries**: Maximum 200 characters
- **Array parameters**: Maximum 1000 items for search results

### Security Features
- **Path validation**: Prevents directory traversal attacks
- **Content sanitization**: Automatic sanitization of user input
- **Base64 detection**: Automatic detection and handling of binary content
- **File type validation**: Validation of file types and extensions
- **Size limits**: Protection against oversized payloads

## Best Practices

### API Usage Guidelines

1. **Always handle errors**: Check for success/failure in responses
2. **Use appropriate timeouts**: Set reasonable timeouts for long operations
3. **Validate inputs**: Validate data before sending to APIs
4. **Use batch operations**: Prefer batch operations over multiple single calls
5. **Monitor performance**: Track API call performance and optimize as needed

### Example Implementation

```typescript
class ChatLoAPIClient {
  async safeAPICall<T>(
    apiCall: () => Promise<T>,
    fallback?: T,
    timeout = 5000
  ): Promise<T> {
    try {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('API timeout')), timeout)
      )

      const result = await Promise.race([apiCall(), timeoutPromise])
      return result as T
    } catch (error) {
      console.error('API call failed:', error)
      if (fallback !== undefined) return fallback
      throw error
    }
  }

  async getConversationsWithFallback() {
    return this.safeAPICall(
      () => window.electronAPI.db.getConversations(),
      [], // Empty array as fallback
      10000 // 10 second timeout
    )
  }
}
```

### Performance Optimization

```typescript
// Batch file operations
const files = ['file1.txt', 'file2.txt', 'file3.txt']
const results = await Promise.all(
  files.map(file => window.electronAPI.vault.readFile(file))
)

// Use appropriate limits for search
const searchResults = await window.electronAPI.files.searchFiles(
  'query',
  50 // Reasonable limit
)

// Cache frequently accessed data
const cache = new Map()
const getCachedConversations = async () => {
  if (cache.has('conversations')) {
    return cache.get('conversations')
  }
  const conversations = await window.electronAPI.db.getConversations()
  cache.set('conversations', conversations)
  return conversations
}
```

---

## Summary

This API reference provides comprehensive documentation for ChatLo's unified IPC system. The system is designed for:

- **Type Safety**: Full TypeScript support with parameter validation
- **Performance**: Optimized for high-throughput operations
- **Reliability**: Comprehensive error handling and recovery
- **Extensibility**: Plugin system for custom functionality
- **Security**: Input validation and sanitization
- **Monitoring**: Built-in metrics and health checks

For additional support or questions about the API system, refer to the source code in `electron/main.ts` and `electron/preload.ts`.
