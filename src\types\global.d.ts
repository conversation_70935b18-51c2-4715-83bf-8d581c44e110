// Global type declarations for ChatLo
declare global {
  interface Window {
    electronAPI: {
      // Generic IPC invoke method for plugin APIs
      invoke: (channel: string, ...args: any[]) => Promise<any>

      // Database operations
      db: {
        getConversations: () => Promise<any[]>
        getConversation: (id: string) => Promise<any>
        createConversation: (title: string) => Promise<string>
        updateConversation: (id: string, title: string) => Promise<void>
        deleteConversation: (id: string) => Promise<void>
        addMessage: (conversationId: string, message: any) => Promise<string>
        getMessages: (conversationId: string) => Promise<any[]>
        togglePinMessage: (messageId: string) => Promise<void>
        updateMessageIntelligence: (messageId: string, entities: string, topics: string, confidence: number) => Promise<void>
        addPinnedIntelligence: (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) => Promise<string>
        getPinnedIntelligence: (messageId: string) => Promise<any>
        getAllPinnedIntelligence: () => Promise<any[]>
        searchConversations: (searchTerm: string) => Promise<any[]>
        getConversationsWithArtifacts: () => Promise<any[]>
        getArtifacts: (messageId: string) => Promise<any[]>
        addArtifact: (messageId: string, artifact: any) => Promise<string>
        updateArtifact: (id: string, updates: any) => Promise<void>
        removeArtifact: (id: string) => Promise<void>
        getConversationArtifacts: (conversationId: string) => Promise<any[]>
        getDatabaseHealth: () => Promise<any>
        createBackup: () => Promise<string>
        openAtPath: (path: string) => Promise<void>
        safeClose: () => Promise<void>
        prepareForDisconnect: () => Promise<void>
        connectPortableDB: (path: string) => Promise<void>
        migrateToPortablePath: (path: string) => Promise<void>
      }

      // Settings
      settings: {
        get: (key: string) => Promise<any>
        set: (key: string, value: any) => Promise<void>
        setPortableMode: (enabled: boolean) => Promise<void>
        setDBPath: (path: string) => Promise<void>
      }

      // File system operations
      files: {
        getVaultRootPath: () => Promise<string>
        setVaultRootPath: (path: string) => Promise<void>
        getChatloFolderPath: () => Promise<string>
        setChatloFolderPath: (path: string) => Promise<void>
        getIndexedFiles: () => Promise<any[]>
        searchFiles: (query: string, limit?: number) => Promise<any[]>
        processFileContent: (fileId: string) => Promise<boolean>
        testDirectParsing: (filePath: string) => Promise<any>
        indexFile: (filePath: string, processContent?: boolean) => Promise<string | null>
        indexVaultFile: (filePath: string, vaultName: string, relativePath: string, processContent?: boolean) => Promise<{ success: boolean; file?: any; error?: string }>
        indexAllFiles: () => Promise<void>
        copyFileToUploads: (sourcePath: string, filename?: string) => Promise<string>
        saveContentToVault: (content: string, filename: string, subfolder?: string) => Promise<string>
        saveContentAsFile: (content: string, filename: string, subfolder?: string) => Promise<string>
        deleteFile: (fileId: string) => Promise<boolean>
        getFileContent: (filePath: string) => Promise<string | null>
        fileExists: (filePath: string) => Promise<boolean>
        showOpenDialog: (options: any) => Promise<any>
        showSaveDialog: (options: any) => Promise<any>
        addFileAttachment: (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => Promise<string>
        getFileAttachments: (messageId: string) => Promise<any[]>
        getMessageFiles: (messageId: string) => Promise<any[]>
        removeFileAttachment: (attachmentId: string) => Promise<void>
        getFileUrl: (filePath: string) => Promise<{ success: boolean; url?: string; error?: string }>
        processFile: (filePath: string) => Promise<{ success: boolean; fileId?: string; error?: string }>
        openPDFViewer: (filePath: string) => Promise<{ success: boolean; error?: string }>
        getMetadata: (filePath: string) => Promise<{ success: boolean; descriptor?: any; error?: string }>
        reindexTree: (vaultPath: string, relativeDir?: string, processContent?: boolean) => Promise<{ success: boolean; indexed?: number; processed?: number; error?: string }>
      }

      // Processing
      processing: {
        enableOCR: (enabled: boolean) => Promise<void>
        getStatus: () => Promise<any>
      }

      // Shell operations
      shell: {
        openPath: (path: string) => Promise<{ success: boolean; error?: string }>
        showItemInFolder: (path: string) => Promise<{ success: boolean; error?: string }>
      }

      // Vault operations
      vault: {
        createDirectory: (dirPath: string) => Promise<{ success: boolean; error?: string }>
        writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>
        appendFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>
        readDirectory: (dirPath: string) => Promise<{
          success: boolean;
          error?: string;
          items: Array<{
            name: string;
            path: string;
            isDirectory: boolean;
            size?: number;
            modified: string;
          }>
        }>
        removeDirectory: (dirPath: string) => Promise<{ success: boolean; error?: string }>
        removeFile: (filePath: string) => Promise<{ success: boolean; error?: string }>
        scanFolder: (folderPath: string) => Promise<{
          success: boolean;
          files?: Array<{
            name: string;
            path: string;
            isDirectory: boolean;
            size: number;
            lastModified: string;
          }>;
          error?: string
        }>
        copyFile: (sourcePath: string, destinationPath: string) => Promise<{ success: boolean; error?: string }>
        pathExists: (targetPath: string) => Promise<{ exists: boolean; error?: string }>
        readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>
        readFileBase64: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>
        getVaultRegistry: () => Promise<any>
        saveVaultRegistry: (registry: any) => Promise<{ success: boolean; error?: string }>
        initializeVaultRoot: (rootPath: string, template: string) => Promise<{ success: boolean; vaults: any[]; error?: string }>
        scanContexts: (vaultPath: string) => Promise<{ success: boolean; contexts: any[]; error?: string }>
      }

      // Auto-updater
      updater: {
        checkForUpdates: () => Promise<{ available: boolean; message?: string; error?: string }>
        downloadAndInstall: () => Promise<{ success: boolean; message?: string; error?: string }>
        onCheckingForUpdate: (callback: () => void) => void
        onUpdateAvailable: (callback: (info: any) => void) => void
        onUpdateNotAvailable: (callback: () => void) => void
        onError: (callback: (message: string) => void) => void
        onDownloadProgress: (callback: (progress: any) => void) => void
        onUpdateDownloaded: (callback: (info: any) => void) => void
      }

      // Plugin management
      plugins: {
        getAll: () => Promise<any[]>
        enable: (pluginId: string, enabled: boolean) => Promise<void>
        disable: (pluginId: string) => Promise<void>
        discover: () => Promise<void>
        getConfig: (pluginId: string) => Promise<any>
        updateConfig: (pluginId: string, config: any) => Promise<void>
        getCapabilities: () => Promise<any>
        getAPIEndpoints: (pluginId: string) => Promise<{ success: boolean; namespace?: string; endpoints?: any[]; error?: string }>
        getAllAPIEndpoints: () => Promise<{ success: boolean; apiInfo?: any[]; error?: string }>
      }

      // Path operations
      path: {
        join: (...parts: string[]) => Promise<{ success: boolean; path?: string; error?: string }>
        normalize: (targetPath: string) => Promise<{ success: boolean; path?: string; error?: string }>
        getContextDirs: (vaultPath: string) => Promise<{ success: boolean; contextDir?: string; filesDir?: string; error?: string }>
      }

      // Events facade
      events: {
        emit: (eventName: string, payload?: any) => Promise<{ success: boolean; error?: string }>
        on: (eventName: string, listener: (payload: any) => void) => () => void
        off: (eventName: string, listener: (payload: any) => void) => void
      }

      // Intelligence operations
      intelligence: {
        write: (targetPath: string, vaultPath: string, data: any) => Promise<{ success: boolean; error?: string }>
        read: (targetPath: string, vaultPath: string) => Promise<{ success: boolean; data?: any; error?: string }>
        save: (targetPath: string, vaultPath: string, data: any) => Promise<{ success: boolean; error?: string }>
        get: (targetPath: string, vaultPath: string) => Promise<{ success: boolean; data?: any; error?: string }>
        analyze: (targetPath: string, vaultPath: string) => Promise<{ success: boolean; result?: any; error?: string }>
        listSessions: (vaultPath: string) => Promise<{ success: boolean; sessions?: any[]; error?: string }>
        writeSession: (targetPath: string, vaultPath: string, data: any) => Promise<{ success: boolean; error?: string }>
      }

      // System operations
      system: {
        getAPIRegistry: () => Promise<any>
        getPerformanceMetrics: () => Promise<any>
        cleanupMiddleware: () => Promise<void>
        getMonitoringData: () => Promise<any>
        getEndpointMetrics: () => Promise<any>
        resetMonitoring: () => Promise<void>
        getErrorStatistics: () => Promise<any>
        clearErrorHistory: () => Promise<void>
      }
    }
  }
}

export {}
