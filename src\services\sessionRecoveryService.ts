import { VaultInfo, ContextInfo } from '../store/sessionStore'

// Vault registry types (matching the actual registry structure)
interface VaultRegistryContext {
  id: string
  name: string
  path: string
  description?: string
}

interface VaultRegistryVault {
  id: string
  name: string
  path: string
  color?: string
  icon?: string
  contexts?: VaultRegistryContext[]
}

interface VaultRegistry {
  version: string
  vaultRoot: string
  vaults: VaultRegistryVault[]
}

// Session recovery service
export class SessionRecoveryService {
  private static instance: SessionRecoveryService
  private lastRegistryQuery: number = 0
  private registryCache: VaultRegistry | null = null
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static getInstance(): SessionRecoveryService {
    if (!SessionRecoveryService.instance) {
      SessionRecoveryService.instance = new SessionRecoveryService()
    }
    return SessionRecoveryService.instance
  }

  /**
   * Get vault registry with caching
   */
  private async getVaultRegistry(): Promise<VaultRegistry | null> {
    try {
      const now = Date.now()
      
      // Use cached registry if still valid
      if (this.registryCache && (now - this.lastRegistryQuery) < this.CACHE_DURATION) {
        console.log('[SESSION-RECOVERY] 📋 Using cached vault registry')
        return this.registryCache
      }

      // Query fresh registry
      console.log('[SESSION-RECOVERY] 🔍 Querying fresh vault registry...')
      const response = await window.electronAPI?.vault?.getVaultRegistry()
      
      if (response && response.success && response.data && response.data.vaults && Array.isArray(response.data.vaults)) {
        this.registryCache = response.data as VaultRegistry
        this.lastRegistryQuery = now
        
        console.log('[SESSION-RECOVERY] ✅ Vault registry updated:', {
          vaultCount: response.data.vaults.length,
          timestamp: new Date(now).toISOString()
        })
        
        return this.registryCache
      } else {
        console.warn('[SESSION-RECOVERY] ⚠️ Invalid vault registry response:', response)
        return null
      }
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to get vault registry:', error)
      return null
    }
  }

  /**
   * Resolve vault and context from IDs
   */
  async resolveContextFromIds(vaultId: string, contextId: string): Promise<{ vault: VaultInfo; context: ContextInfo } | null> {
    try {
      console.log('[SESSION-RECOVERY] 🔍 Resolving context from IDs:', { vaultId, contextId })
      
      const registry = await this.getVaultRegistry()
      if (!registry) {
        console.warn('[SESSION-RECOVERY] ⚠️ No vault registry available')
        return null
      }

      // Find vault by ID
      const vault = registry.vaults.find(v => v.id === vaultId)
      if (!vault) {
        console.warn('[SESSION-RECOVERY] ⚠️ Vault not found:', vaultId)
        return null
      }

      // Find context by ID within vault
      const context = vault.contexts?.find(c => c.id === contextId)
      if (!context) {
        console.warn('[SESSION-RECOVERY] ⚠️ Context not found in vault:', { vaultId, contextId })
        return null
      }

      // Convert to session store types
      const vaultInfo: VaultInfo = {
        id: vault.id,
        name: vault.name,
        path: vault.path,
        color: vault.color,
        icon: vault.icon
      }

      const contextInfo: ContextInfo = {
        id: context.id,
        name: context.name,
        path: context.path,
        vaultId: vault.id,
        description: context.description
      }

      console.log('[SESSION-RECOVERY] ✅ Context resolved successfully:', {
        vault: vault.name,
        context: context.name
      })

      return { vault: vaultInfo, context: contextInfo }
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to resolve context from IDs:', error)
      return null
    }
  }

  /**
   * Get default vault and context
   */
  async getDefaultContext(): Promise<{ vault: VaultInfo; context: ContextInfo } | null> {
    try {
      console.log('[SESSION-RECOVERY] 🔍 Getting default context...')
      
      const registry = await this.getVaultRegistry()
      if (!registry || !registry.vaults || registry.vaults.length === 0) {
        console.warn('[SESSION-RECOVERY] ⚠️ No vaults available for default context')
        return null
      }

      // Use first vault and first context as default
      const defaultVault = registry.vaults[0]
      if (!defaultVault.contexts || defaultVault.contexts.length === 0) {
        console.warn('[SESSION-RECOVERY] ⚠️ No contexts available in default vault')
        return null
      }

      const defaultContext = defaultVault.contexts[0]

      // Convert to session store types
      const vaultInfo: VaultInfo = {
        id: defaultVault.id,
        name: defaultVault.name,
        path: defaultVault.path,
        color: defaultVault.color,
        icon: defaultVault.icon
      }

      const contextInfo: ContextInfo = {
        id: defaultContext.id,
        name: defaultContext.name,
        path: defaultContext.path,
        vaultId: defaultVault.id,
        description: defaultContext.description
      }

      console.log('[SESSION-RECOVERY] ✅ Default context found:', {
        vault: vault.name,
        context: context.name
      })

      return { vault: vaultInfo, context: contextInfo }
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to get default context:', error)
      return null
    }
  }

  /**
   * Get all available vaults and contexts
   */
  async getAllAvailableContexts(): Promise<{ vault: VaultInfo; contexts: ContextInfo[] }[]> {
    try {
      const registry = await this.getVaultRegistry()
      if (!registry || !registry.vaults) {
        return []
      }

      return registry.vaults.map(vault => ({
        vault: {
          id: vault.id,
          name: vault.name,
          path: vault.path,
          color: vault.color,
          icon: vault.icon
        },
        contexts: (vault.contexts || []).map(context => ({
          id: context.id,
          name: context.name,
          path: context.path,
          vaultId: vault.id,
          description: context.description
        }))
      }))
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to get all available contexts:', error)
      return []
    }
  }

  /**
   * Validate vault and context IDs
   */
  async validateContextIds(vaultId: string, contextId: string): Promise<boolean> {
    try {
      const resolved = await this.resolveContextFromIds(vaultId, contextId)
      return resolved !== null
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to validate context IDs:', error)
      return false
    }
  }

  /**
   * Clear registry cache (useful for testing or when registry changes)
   */
  clearCache(): void {
    this.registryCache = null
    this.lastRegistryQuery = 0
    console.log('[SESSION-RECOVERY] 🧹 Registry cache cleared')
  }
}

// Export singleton instance
export const sessionRecoveryService = SessionRecoveryService.getInstance()
export default sessionRecoveryService
