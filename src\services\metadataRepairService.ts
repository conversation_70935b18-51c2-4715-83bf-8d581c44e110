/**
 * Metadata Repair Service
 * Fixes corrupted metadata.json files that contain JavaScript object syntax instead of valid JSON
 */

import { generateContentId } from '../utils/idGenerator'

export class MetadataRepairService {
  
  /**
   * Repair a corrupted metadata file
   */
  async repairMetadataFile(filePath: string, content: string): Promise<string | null> {
    try {
      console.log(`🔧 [METADATA-REPAIR] Attempting to repair: ${filePath}`)
      console.log(`🔧 [METADATA-REPAIR] Content preview: ${content.substring(0, 200)}...`)
      
      try {
        // Attempt to fix common JavaScript object syntax issues
        const repairedContent = this.convertJSObjectToJSON(content)
        
        // Validate the repaired content
        const parsed = JSON.parse(repairedContent)
        
        // Ensure required fields exist
        const validatedMetadata = this.validateAndEnhanceMetadata(parsed, filePath)
        
        const finalContent = JSON.stringify(validatedMetadata, null, 2)
        
        console.log(`✅ [METADATA-REPAIR] Successfully repaired metadata: ${filePath}`)
        return finalContent
        
      } catch (repairError) {
        console.error(`❌ [METADATA-REPAIR] Failed to repair metadata: ${filePath}`, repairError)
        
        // Generate a completely new metadata file as fallback
        console.log(`🆘 [METADATA-REPAIR] Generating fallback metadata for: ${filePath}`)
        const fallbackContent = this.generateFallbackMetadata(filePath)
        
        // Try to write the fallback metadata immediately
        try {
          const writeResult = await window.electronAPI.vault.writeFile(filePath, fallbackContent)
          if (writeResult.success) {
            console.log(`✅ [METADATA-REPAIR] Successfully wrote fallback metadata: ${filePath}`)
            return fallbackContent
          } else {
            console.error(`❌ [METADATA-REPAIR] Failed to write fallback metadata: ${filePath}`, writeResult.error)
            return null
          }
        } catch (writeError) {
          console.error(`❌ [METADATA-REPAIR] Error writing fallback metadata: ${filePath}`, writeError)
          return null
        }
      }
    } catch (error) {
      console.error(`💥 [METADATA-REPAIR] Critical error in repair process: ${filePath}`, error)
      
      // Last resort: return fallback metadata without writing
      console.log(`🆘 [METADATA-REPAIR] Returning fallback metadata without writing: ${filePath}`)
      return this.generateFallbackMetadata(filePath)
    }
  }

  /**
   * Convert JavaScript object syntax to valid JSON
   */
  private convertJSObjectToJSON(content: string): string {
    let repaired = content.trim()
    
    // Handle completely corrupted or truncated content
    if (!repaired.startsWith('{') || !repaired.includes('}')) {
      console.warn(`⚠️ [METADATA-REPAIR] Content is severely corrupted, cannot repair: ${content.substring(0, 100)}...`)
      throw new Error('Content is severely corrupted and cannot be repaired')
    }
    
    // Find the last complete object by looking for matching braces
    let braceCount = 0
    let lastValidPosition = 0
    
    for (let i = 0; i < repaired.length; i++) {
      if (repaired[i] === '{') braceCount++
      if (repaired[i] === '}') braceCount--
      
      if (braceCount === 0 && repaired[i] === '}') {
        lastValidPosition = i + 1
        break
      }
    }
    
    // Truncate to last valid position if content is incomplete
    if (lastValidPosition > 0 && lastValidPosition < repaired.length) {
      repaired = repaired.substring(0, lastValidPosition)
      console.log(`🔧 [METADATA-REPAIR] Truncated incomplete content to valid JSON`)
    }
    
    // Remove any trailing commas before closing braces/brackets
    repaired = repaired.replace(/,(\s*[}\]])/g, '$1')
    
    // Add quotes around unquoted property names (more robust pattern)
    repaired = repaired.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
    
    // Add quotes around unquoted string values (but not numbers, booleans, null, or file paths)
    repaired = repaired.replace(/:(\s*)([a-zA-Z_$][a-zA-Z0-9_$\-\s\\\/:]*[a-zA-Z0-9_$])(\s*[,}])/g, (match, space1, value, space2) => {
      const trimmedValue = value.trim()
      
      // Don't quote if it's a boolean, null, or looks like a number
      if (['true', 'false', 'null'].includes(trimmedValue) || /^\d+(\.\d+)?$/.test(trimmedValue)) {
        return match
      }
      
      // Don't quote if it looks like a file path (contains backslashes or forward slashes)
      if (trimmedValue.includes('\\') || trimmedValue.includes('/') || trimmedValue.includes(':')) {
        return match
      }
      
      // Don't quote if it looks like a URL or protocol
      if (trimmedValue.includes('://') || trimmedValue.startsWith('http')) {
        return match
      }
      
      return `:${space1}"${trimmedValue}"${space2}`
    })
    
    // Fix color values (hex colors without quotes)
    repaired = repaired.replace(/:(\s*)(#[a-fA-F0-9]{6})(\s*[,}])/g, ':"$2"$3')
    
    // Fix icon values (fa- prefixed values without quotes)
    repaired = repaired.replace(/:(\s*)(fa-[a-zA-Z0-9\-]+)(\s*[,}])/g, ':"$2"$3')
    
    // Fix ISO date strings without quotes
    repaired = repaired.replace(/:(\s*)(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)(\s*[,}])/g, ':"$2"$3')
    
    // Fix Windows file paths that are missing quotes (specific fix for the reported error)
    repaired = repaired.replace(/:(\s*)([A-Z]:\\[^,\s}]+)(\s*[,}])/g, ':"$2"$3')
    
    // Fix common corruption patterns
    repaired = repaired.replace(/,\s*}/g, '}') // Remove trailing commas
    repaired = repaired.replace(/,\s*]/g, ']') // Remove trailing commas in arrays
    
    // Ensure the content ends properly
    if (!repaired.endsWith('}')) {
      repaired = repaired + '}'
    }
    
    console.log(`🔧 [METADATA-REPAIR] Repaired content: ${repaired.substring(0, 200)}...`)
    
    return repaired
  }

  /**
   * Validate and enhance metadata object
   */
  private validateAndEnhanceMetadata(metadata: any, filePath: string): any {
    const enhanced = { ...metadata }
    
    // Ensure required fields exist
    if (!enhanced.id) {
      enhanced.id = generateContentId(filePath, 'ctx')
    }
    
    if (!enhanced.name) {
      // Extract name from file path
      const pathParts = filePath.split(/[\/\\]/)
      const contextFolder = pathParts[pathParts.length - 3] // Assuming path ends with /.intelligence/metadata.json
      enhanced.name = contextFolder || 'Unknown Context'
    }
    
    if (!enhanced.created) {
      enhanced.created = new Date().toISOString()
    }
    
    if (!enhanced.description) {
      enhanced.description = `Context vault for ${enhanced.name}`
    }
    
    if (!enhanced.color) {
      enhanced.color = '#8AB0BB' // Default teal color
    }
    
    if (!enhanced.icon) {
      enhanced.icon = 'fa-folder'
    }
    
    if (!enhanced.contextType) {
      enhanced.contextType = 'project'
    }
    
    return enhanced
  }

  /**
   * Generate a completely new metadata file as fallback
   */
  private generateFallbackMetadata(filePath: string): string {
    const pathParts = filePath.split(/[\/\\]/)
    
    // Extract context information from path
    let contextFolder = 'Unknown Context'
    let vaultName = 'Unknown Vault'
    
    // Look for context folder (usually 3 levels up from .intelligence/metadata.json)
    for (let i = pathParts.length - 1; i >= 0; i--) {
      if (pathParts[i] === '.intelligence') {
        if (i > 0) {
          contextFolder = pathParts[i - 1]
        }
        // Look for vault name (usually 2 levels up from context)
        if (i > 2) {
          vaultName = pathParts[i - 2]
        }
        break
      }
    }
    
    // Generate intelligent defaults based on context
    const defaultColor = this.getDefaultColorForContext(contextFolder, vaultName)
    const defaultIcon = this.getDefaultIconForContext(contextFolder, vaultName)
    const contextType = this.getDefaultContextType(contextFolder, vaultName)
    
    const fallbackMetadata = {
      id: generateContentId(filePath, 'ctx'),
      name: contextFolder,
      created: new Date().toISOString(),
      description: `Context vault for ${contextFolder} in ${vaultName}`,
      color: defaultColor,
      icon: defaultIcon,
      contextType: contextType,
      repaired: true,
      repairedAt: new Date().toISOString(),
      originalPath: filePath,
      repairReason: 'Metadata file was corrupted and could not be repaired'
    }
    
    console.log(`🆘 [METADATA-REPAIR] Generated fallback metadata for: ${filePath}`)
    console.log(`🆘 [METADATA-REPAIR] Context: ${contextFolder}, Vault: ${vaultName}, Color: ${defaultColor}`)
    
    return JSON.stringify(fallbackMetadata, null, 2)
  }

  /**
   * Get default color based on context and vault names
   */
  private getDefaultColorForContext(contextName: string, vaultName: string): string {
    const lowerContext = contextName.toLowerCase()
    const lowerVault = vaultName.toLowerCase()
    
    // Context-specific colors
    if (lowerContext.includes('getting-started') || lowerContext.includes('welcome')) {
      return '#8AB0BB' // Teal
    }
    if (lowerContext.includes('project') || lowerContext.includes('work')) {
      return '#FF8383' // Red
    }
    if (lowerContext.includes('personal') || lowerContext.includes('home')) {
      return '#8AB0BB' // Teal
    }
    if (lowerContext.includes('study') || lowerContext.includes('learning')) {
      return '#FFB366' // Orange
    }
    
    // Vault-specific colors
    if (lowerVault.includes('work')) {
      return '#FF8383' // Red
    }
    if (lowerVault.includes('personal')) {
      return '#8AB0BB' // Teal
    }
    
    // Default color based on hash of context name for consistency
    const hash = contextName.split('').reduce((a, b) => {
      a = ((a << 5) - a + b.charCodeAt(0)) & 0xffffffff
      return a
    }, 0)
    
    const colors = ['#8AB0BB', '#FF8383', '#FFB366', '#A8E6CF', '#FFD3B6']
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * Get default icon based on context and vault names
   */
  private getDefaultIconForContext(contextName: string, vaultName: string): string {
    const lowerContext = contextName.toLowerCase()
    
    if (lowerContext.includes('getting-started') || lowerContext.includes('welcome')) {
      return 'fa-lightbulb'
    }
    if (lowerContext.includes('project') || lowerContext.includes('work')) {
      return 'fa-folder-open'
    }
    if (lowerContext.includes('personal') || lowerContext.includes('home')) {
      return 'fa-user'
    }
    if (lowerContext.includes('study') || lowerContext.includes('learning')) {
      return 'fa-graduation-cap'
    }
    
    return 'fa-folder'
  }

  /**
   * Get default context type based on context and vault names
   */
  private getDefaultContextType(contextName: string, vaultName: string): string {
    const lowerContext = contextName.toLowerCase()
    
    if (lowerContext.includes('getting-started') || lowerContext.includes('welcome')) {
      return 'getting-started'
    }
    if (lowerContext.includes('project') || lowerContext.includes('work')) {
      return 'project'
    }
    if (lowerContext.includes('personal') || lowerContext.includes('home')) {
      return 'personal'
    }
    if (lowerContext.includes('study') || lowerContext.includes('learning')) {
      return 'study'
    }
    
    return 'project'
  }

  /**
   * Repair all corrupted metadata files in a vault
   */
  async repairVaultMetadata(vaultPath: string): Promise<number> {
    let repairedCount = 0
    
    try {
      // Scan for .intelligence directories
      const scanResult = await this.scanForIntelligenceDirectories(vaultPath)
      
      for (const intelligenceDir of scanResult) {
        const metadataPath = `${intelligenceDir}/metadata.json`
        
        try {
          const readResult = await window.electronAPI.vault.readFile(metadataPath)
          
          if (readResult.success && readResult.content) {
            const repairedContent = await this.repairMetadataFile(metadataPath, readResult.content)
            
            if (repairedContent) {
              // Write the repaired content back
              const writeResult = await window.electronAPI.vault.writeFile(metadataPath, repairedContent)
              
              if (writeResult.success) {
                repairedCount++
                console.log(`✅ [METADATA-REPAIR] Repaired: ${metadataPath}`)
              } else {
                console.error(`❌ [METADATA-REPAIR] Failed to write repaired metadata: ${metadataPath}`)
              }
            }
          }
        } catch (error) {
          console.error(`❌ [METADATA-REPAIR] Error processing: ${metadataPath}`, error)
        }
      }
      
    } catch (error) {
      console.error(`❌ [METADATA-REPAIR] Error scanning vault: ${vaultPath}`, error)
    }
    
    return repairedCount
  }

  /**
   * Proactively repair all corrupted metadata files in a vault
   * This should be called before scanning to prevent cascade failures
   */
  async repairVaultMetadataProactively(vaultPath: string): Promise<{
    totalFiles: number
    repairedFiles: number
    failedFiles: number
    errors: string[]
  }> {
    const result = {
      totalFiles: 0,
      repairedFiles: 0,
      failedFiles: 0,
      errors: [] as string[]
    }
    
    try {
      console.log(`🔧 [METADATA-REPAIR] Starting proactive repair for vault: ${vaultPath}`)
      
      // Scan for .intelligence directories
      const scanResult = await this.scanForIntelligenceDirectories(vaultPath)
      result.totalFiles = scanResult.length
      
      console.log(`🔧 [METADATA-REPAIR] Found ${scanResult.length} intelligence directories to check`)
      
      for (const intelligenceDir of scanResult) {
        const metadataPath = `${intelligenceDir}/metadata.json`
        
        try {
          const readResult = await window.electronAPI.vault.readFile(metadataPath)
          
          if (readResult.success && readResult.content) {
            // Check if content is valid JSON
            try {
              JSON.parse(readResult.content)
              // Valid JSON, skip
              continue
            } catch (parseError) {
              // Invalid JSON, attempt repair
              console.log(`🔧 [METADATA-REPAIR] Found corrupted metadata: ${metadataPath}`)
              
              const repairedContent = await this.repairMetadataFile(metadataPath, readResult.content)
              
              if (repairedContent) {
                result.repairedFiles++
                console.log(`✅ [METADATA-REPAIR] Successfully repaired: ${metadataPath}`)
              } else {
                result.failedFiles++
                const errorMsg = `Failed to repair: ${metadataPath}`
                result.errors.push(errorMsg)
                console.error(`❌ [METADATA-REPAIR] ${errorMsg}`)
              }
            }
          }
        } catch (error) {
          result.failedFiles++
          const errorMsg = `Error processing ${metadataPath}: ${error}`
          result.errors.push(errorMsg)
          console.error(`❌ [METADATA-REPAIR] ${errorMsg}`)
        }
      }
      
      console.log(`🔧 [METADATA-REPAIR] Proactive repair complete for vault: ${vaultPath}`)
      console.log(`🔧 [METADATA-REPAIR] Results: ${result.repairedFiles} repaired, ${result.failedFiles} failed`)
      
    } catch (error) {
      const errorMsg = `Error scanning vault: ${vaultPath} - ${error}`
      result.errors.push(errorMsg)
      console.error(`❌ [METADATA-REPAIR] ${errorMsg}`)
    }
    
    return result
  }

  /**
   * Scan for .intelligence directories recursively
   */
  private async scanForIntelligenceDirectories(rootPath: string): Promise<string[]> {
    const intelligenceDirectories: string[] = []
    
    try {
      const scanResult = await window.electronAPI.vault.readDirectory(rootPath)
      
      if (scanResult.success && scanResult.items) {
        for (const item of scanResult.items) {
          if (item.isDirectory) {
            if (item.name === '.intelligence') {
              intelligenceDirectories.push(item.path)
            } else if (!item.name.startsWith('.')) {
              // Recursively scan subdirectories (but skip other hidden directories)
              const subDirectories = await this.scanForIntelligenceDirectories(item.path)
              intelligenceDirectories.push(...subDirectories)
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory: ${rootPath}`, error)
    }
    
    return intelligenceDirectories
  }
}

export const metadataRepairService = new MetadataRepairService()
