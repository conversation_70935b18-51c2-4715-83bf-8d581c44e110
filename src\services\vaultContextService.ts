import { BaseService } from './base/BaseService'
import { ServiceError, ServiceErrorCode } from './base/ServiceError'
import { useSessionStore } from '../store/sessionStore'

export interface VaultContext {
  vaultPath: string
  contextId: string
  contextName: string
  contextPath: string
}

export interface PathResolution {
  type: 'file' | 'annotation' | 'vault-relative'
  vault: string
  context: string
  path: string
  originalPath: string
}

/**
 * VaultContextService - Simplified with Session Integration
 * Handles vault and context resolution using the session store
 */
export class VaultContextService extends BaseService {
  private static instance: VaultContextService
  private contextListeners: Array<(context: VaultContext | null) => void> = []

  constructor() {
    super({
      name: 'VaultContextService',
      logLevel: 'info',
      enableConsoleLogging: true,
      autoInitialize: true
    })
  }

  static getInstance(): VaultContextService {
    if (!VaultContextService.instance) {
      VaultContextService.instance = new VaultContextService()
    }
    return VaultContextService.instance
  }

  /**
   * Get current context from session store
   */
  async getCurrentContext(): Promise<VaultContext | null> {
    try {
      const currentContext = useSessionStore.getState().getCurrentContext()
      const currentVault = useSessionStore.getState().getCurrentVault()
      
      if (!currentContext || !currentVault) {
        console.log('[VAULT-CONTEXT] ℹ️ No current context available')
        return null
      }

      const vaultContext: VaultContext = {
        vaultPath: currentVault.path,
        contextId: currentContext.id,
        contextName: currentContext.name,
        contextPath: currentContext.path
      }

      console.log('[VAULT-CONTEXT] ✅ Current context:', {
        vault: currentVault.name,
        context: currentContext.name,
        path: currentContext.path
      })

      return vaultContext
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Error getting current context:', error)
      return null
    }
  }

  /**
   * Set current context in session store
   */
  async setCurrentContext(context: VaultContext): Promise<void> {
    try {
      // Convert to session store format
      const vaultInfo = {
        id: context.contextId, // We'll need to get the actual vault ID
        name: 'Vault', // We'll need to get the actual vault name
        path: context.vaultPath
      }

      const contextInfo = {
        id: context.contextId,
        name: context.contextName,
        path: context.contextPath,
        vaultId: context.contextId // We'll need to get the actual vault ID
      }

      // Set in session store
      useSessionStore.getState().setCurrentContext(vaultInfo, contextInfo)
      
      // Notify listeners
      this.notifyContextListeners(context)
      
      console.log('[VAULT-CONTEXT] ✅ Current context set:', {
        vault: context.vaultPath,
        context: context.contextName
      })
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Error setting current context:', error)
      throw new ServiceError(
        ServiceErrorCode.OPERATION_FAILED,
        'Failed to set current context',
        { serviceName: this.serviceName, operation: 'setCurrentContext', details: { error } }
      )
    }
  }

  /**
   * Subscribe to context changes
   */
  subscribe(listener: (context: VaultContext | null) => void): () => void {
    this.contextListeners.push(listener)
    
    // Immediately notify with current context
    this.getCurrentContext().then(context => {
      try {
        listener(context)
      } catch (error) {
        console.error('[VAULT-CONTEXT] ❌ Error in context listener:', error)
      }
    })
    
    return () => {
      const index = this.contextListeners.indexOf(listener)
      if (index > -1) {
        this.contextListeners.splice(index, 1)
      }
    }
  }

  /**
   * Resolve any path type to a standardized PathResolution
   * This replaces all isVirtualPath logic
   */
  async resolvePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    try {
      console.log('[VAULT-CONTEXT] 🔍 Resolving path:', { inputPath, options })
      
      // Determine path type
      const isAnnotationPath = inputPath.startsWith('chat-notes/')
      const isAbsolutePath = /^[A-Z]:\\/.test(inputPath) || inputPath.startsWith('/')
      
      console.log('[VAULT-CONTEXT] 🔍 Path type detection:', { 
        isAnnotationPath, 
        isAbsolutePath, 
        inputPath 
      })
      
      if (isAnnotationPath) {
        console.log('[VAULT-CONTEXT] 📝 Resolving annotation path...')
        return await this.resolveAnnotationPath(inputPath, options)
      } else if (isAbsolutePath) {
        console.log('[VAULT-CONTEXT] 📁 Resolving absolute file path...')
        return await this.resolveFilePath(inputPath)
      } else {
        console.log('[VAULT-CONTEXT] 📂 Resolving vault-relative path...')
        return await this.resolveVaultRelativePath(inputPath, options)
      }
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Failed to resolve path:', error)
      throw new ServiceError(
        ServiceErrorCode.VALIDATION_ERROR,
        `Failed to resolve path: ${inputPath}`,
        { serviceName: this.serviceName, operation: 'resolvePath', details: { inputPath, error } }
      )
    }
  }

  /**
   * Resolve annotation paths (chat-notes/...)
   */
  private async resolveAnnotationPath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    try {
      console.log('[VAULT-CONTEXT] 📝 resolveAnnotationPath called with:', { inputPath, options })
      
      // Get current context from session store
      const currentContext = await this.getCurrentContext()
      if (!currentContext) {
        throw new Error('No current context available for annotation path resolution')
      }

      console.log('[VAULT-CONTEXT] 📝 Got current context:', currentContext)
      
      // Convert chat-notes/file.json → .intelligence/context-notes/file.json
      const fileName = inputPath.replace('chat-notes/', '')
      const resolvedPath = `${currentContext.contextPath}/.intelligence/context-notes/${fileName}`
      
      console.log('[VAULT-CONTEXT] 📝 Resolved annotation path:', {
        inputPath,
        resolvedPath,
        context: currentContext.contextName
      })
      
      return {
        type: 'annotation',
        vault: currentContext.vaultPath,
        context: currentContext.contextId,
        path: resolvedPath,
        originalPath: inputPath
      }
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Failed to resolve annotation path:', error)
      throw error
    }
  }

  /**
   * Resolve absolute file paths
   */
  private async resolveFilePath(inputPath: string): Promise<PathResolution> {
    try {
      console.log('[VAULT-CONTEXT] 📁 Resolving absolute file path:', inputPath)
      
      // For absolute paths, we need to infer the vault and context
      // This is a simplified version - in practice, you might want more sophisticated logic
      
      // Extract context from path (simplified)
      const pathParts = inputPath.split(/[\\\/]/)
      const contextId = pathParts[pathParts.length - 2] || 'default'
      const vaultPath = pathParts.slice(0, -2).join('/')
      
      console.log('[VAULT-CONTEXT] 📁 Extracted path info:', { vaultPath, contextId })
      
      return {
        type: 'file',
        vault: vaultPath,
        context: contextId,
        path: inputPath,
        originalPath: inputPath
      }
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Failed to resolve file path:', error)
      throw error
    }
  }

  /**
   * Resolve vault-relative paths (documents/file.pdf)
   */
  private async resolveVaultRelativePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): Promise<PathResolution> {
    try {
      console.log('[VAULT-CONTEXT] 📂 Resolving vault-relative path:', { inputPath, options })
      
      // Get current context from session store
      const currentContext = await this.getCurrentContext()
      if (!currentContext) {
        throw new Error('No current context available for vault-relative path resolution')
      }

      console.log('[VAULT-CONTEXT] 📂 Got current context:', currentContext)
      
      const resolvedPath = `${currentContext.contextPath}/${inputPath}`
      
      console.log('[VAULT-CONTEXT] 📂 Resolved vault-relative path:', {
        inputPath,
        resolvedPath,
        context: currentContext.contextName
      })
      
      return {
        type: 'vault-relative',
        vault: currentContext.vaultPath,
        context: currentContext.contextId,
        path: resolvedPath,
        originalPath: inputPath
      }
    } catch (error) {
      console.error('[VAULT-CONTEXT] ❌ Failed to resolve vault-relative path:', error)
      throw error
    }
  }

  /**
   * Notify all context listeners
   */
  private notifyContextListeners(context: VaultContext | null): void {
    this.contextListeners.forEach(listener => {
      try {
        listener(context)
      } catch (error) {
        console.error('[VAULT-CONTEXT] ❌ Error in context listener:', error)
      }
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    try {
      console.log('[VAULT-CONTEXT] 🚀 Initializing VaultContextService...')
      
      // Check if we have a current context
      const currentContext = await this.getCurrentContext()
      if (currentContext) {
        console.log('[VAULT-CONTEXT] ✅ Service initialized with current context:', currentContext.contextName)
      } else {
        console.log('[VAULT-CONTEXT] ℹ️ Service initialized without current context')
      }
      
      this.logger.info('VaultContextService initialized successfully', 'doInitialize')
    } catch (error) {
      this.logger.warn('Failed to initialize VaultContextService', 'doInitialize', error)
    }
  }
}

// Export singleton instance
export const vaultContextService = VaultContextService.getInstance()
export default vaultContextService
