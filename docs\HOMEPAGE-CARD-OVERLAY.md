# HomePage Card Overlay - Features Planning & Implementation

**Last Updated:** 2025-08-26  
**Purpose:** Design and plan the context card overlay system for HomePage, including glance view and master mode navigation

## Executive Summary

This document outlines the comprehensive overlay system for HomePage context cards, designed to provide users with quick context information (glance view) and seamless navigation to the full context vault experience (master mode). The system balances information density with user experience, ensuring users can quickly assess context relevance while maintaining easy access to detailed functionality.

## 🎯 **OVERLAY DESIGN PHILOSOPHY**

### **Core Principles:**
1. **Glance View First**: Quick context assessment without overwhelming detail
2. **Progressive Disclosure**: Information revealed based on user interaction depth
3. **Master Mode Gateway**: Clear path to full context vault functionality
4. **Activity Awareness**: Red card highlighting integration for "currently working on" contexts
5. **Performance Optimized**: Lightweight overlay with minimal rendering impact

### **User Experience Goals:**
- **2-3 second context assessment** for each card
- **Single click access** to master mode
- **Visual hierarchy** that guides user attention
- **Consistent interaction patterns** across all overlays

---

## 🔍 **OVERLAY ARCHITECTURE OVERVIEW**

### **Component Structure:**
```
HomePage Context Card
├── Card Base (existing)
├── Overlay Trigger (hover/click)
├── Glance View Overlay
│   ├── Context Summary
│   ├── Recent Activity
│   ├── Quick Actions
│   └── Master Mode Button
└── Master Mode Navigation
    └── Full Context Vault Experience
```

### **Overlay States:**
1. **Default State**: Context card with basic information
2. **Hover State**: Subtle visual enhancement, overlay preview
3. **Glance View**: Detailed context information overlay
4. **Master Mode**: Full context vault interface

---

## 📱 **GLANCE VIEW OVERLAY DESIGN**

### **Layout Structure:**
```
┌─────────────────────────────────────┐
│ Context Header                      │
│ ┌─────────────────────────────────┐ │
│ │ Context Name & Type             │ │
│ │ Last Activity & Status          │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Activity Summary                    │
│ ┌─────────────────────────────────┐ │
│ │ Recent Files (3-5 items)       │ │
│ │ Recent Chats (2-3 items)       │ │
│ │ Intelligence Status             │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Quick Actions                      │
│ ┌─────────────────────────────────┐ │
│ │ [💬 Chat] [📁 Files] [🔍 View] │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Master Mode Navigation             │
│ ┌─────────────────────────────────┐ │
│ │ [🚀 Enter Master Mode]         │ │
│ │ Full Context Vault Experience  │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### **Content Sections:**

#### **1. Context Header**
- **Context Name**: Large, prominent display
- **Context Type**: Badge-style indicator
- **Last Activity**: Human-readable timestamp (e.g., "2 hours ago")
- **Status Indicators**: Processing, ready, needs attention

#### **2. Activity Summary**
- **Recent Files**: 3-5 most recently accessed files with icons
- **Recent Chats**: 2-3 most recent chat sessions with preview
- **Intelligence Status**: Processing progress, completion indicators
- **Activity Metrics**: File count, chat count, intelligence depth

#### **3. Quick Actions**
- **💬 Chat**: Direct navigation to context chat
- **📁 Files**: Direct navigation to context files
- **🔍 View**: Expand overlay for more details
- **⚡ Quick Upload**: Drag & drop file upload

#### **4. Master Mode Navigation**
- **🚀 Enter Master Mode**: Primary call-to-action button
- **Full Context Vault Experience**: Descriptive text
- **Navigation Preview**: Brief description of what master mode offers

---

## 🚀 **MASTER MODE INTEGRATION**

### **Master Mode Button Design:**
```
┌─────────────────────────────────────┐
│ 🚀 Enter Master Mode               │
│ Full Context Vault Experience      │
│ ─────────────────────────────────── │
│ • Complete file management         │
│ • Advanced intelligence tools      │
│ • Deep context exploration         │
│ • AI-powered organization          │
└─────────────────────────────────────┘
```

### **Master Mode Features (Non-Editable):**
- **Complete File Management**: Full file tree, search, organization
- **Advanced Intelligence Tools**: Smart annotations, AI analysis
- **Deep Context Exploration**: Full context history, relationships
- **AI-Powered Organization**: Automated file categorization, insights
- **Collaboration Tools**: Sharing, commenting, version control

### **Navigation Flow:**
```
Glance View → Master Mode Button → Full Context Vault
     ↓              ↓                    ↓
Quick Info → Detailed Preview → Complete Experience
```

---

## 🎨 **VISUAL DESIGN SPECIFICATIONS**

### **Color Scheme:**
- **Primary Background**: `bg-gray-800/95` (semi-transparent)
- **Secondary Background**: `bg-gray-700/80` (section backgrounds)
- **Accent Colors**: Context-specific color scheme from card
- **Text Colors**: `text-gray-100` (primary), `text-gray-300` (secondary)
- **Border Colors**: `border-gray-600` (subtle separation)

### **Typography:**
- **Context Name**: `text-xl font-semibold` (large, prominent)
- **Section Headers**: `text-sm font-medium text-gray-300` (medium weight)
- **Content Text**: `text-sm text-gray-400` (readable, secondary)
- **Action Text**: `text-sm font-medium` (buttons, links)

### **Spacing & Layout:**
- **Overlay Padding**: `p-6` (generous breathing room)
- **Section Spacing**: `mb-4` (clear visual separation)
- **Content Padding**: `p-3` (internal section padding)
- **Button Spacing**: `gap-2` (consistent action spacing)

### **Animation & Transitions:**
- **Overlay Entry**: `fade-in` with `scale-95` to `scale-100`
- **Hover Effects**: Subtle `hover:scale-105` on interactive elements
- **State Transitions**: Smooth `transition-all duration-200`
- **Loading States**: Skeleton loading for dynamic content

---

## ⚡ **INTERACTION PATTERNS**

### **Trigger Mechanisms:**
1. **Hover Trigger**: `onMouseEnter` - Shows overlay preview
2. **Click Trigger**: `onClick` - Expands to full glance view
3. **Focus Trigger**: `onFocus` - Keyboard accessibility support
4. **Auto-Trigger**: Red card contexts show overlay automatically

### **Dismissal Patterns:**
1. **Mouse Leave**: `onMouseLeave` - Hides overlay after delay
2. **Click Outside**: `onClickOutside` - Closes overlay
3. **Escape Key**: `onKeyDown` - Keyboard dismissal
4. **Navigation**: Master mode button - Transitions to full experience

### **State Management:**
```typescript
interface OverlayState {
  isVisible: boolean
  isExpanded: boolean
  contextId: string | null
  overlayType: 'preview' | 'glance' | 'master'
  lastActivity: ActivityData | null
  isLoading: boolean
}
```

---

## 🔧 **TECHNICAL IMPLEMENTATION**

### **Component Architecture:**
```typescript
// Main overlay component
const ContextCardOverlay: React.FC<{
  context: ContextVaultCard
  isVisible: boolean
  onClose: () => void
  onMasterMode: (contextId: string) => void
}> = ({ context, isVisible, onClose, onMasterMode }) => {
  // Implementation details
}

// Overlay sections
const OverlayHeader = ({ context }) => { /* ... */ }
const ActivitySummary = ({ context }) => { /* ... */ }
const QuickActions = ({ context, onAction }) => { /* ... */ }
const MasterModeButton = ({ context, onMasterMode }) => { /* ... */ }
```

### **Data Integration:**
- **Context Data**: From existing `ContextVaultCard` interface
- **Recent Chats**: `db.conversations.getAll()` via `UnifiedAPIClient`
- **Recent Files**: `vault.readDirectory(path)` via `UnifiedAPIClient`
- **Master Preview**: `vault.readFile(path + '/master.md')` via `UnifiedAPIClient`
- **File Dialog**: `files.showOpenDialog(options)` via `UnifiedAPIClient`
- **Real-time Updates**: Unified `events:*` where applicable

### **Performance Considerations:**
- **Lazy Loading**: Overlay content loaded on demand
- **Virtual Scrolling**: For large file/chat lists
- **Image Optimization**: Thumbnails and lazy image loading
- **Memory Management**: Overlay cleanup on unmount

---

## 🔧 **UNIFIEDAPICLIENT INTEGRATION REQUIREMENTS**

### **UnifiedAPIClient Integration Status**
**Current Status:** ✅ **100% INTEGRATED** - HomePage card overlay will use UnifiedAPIClient
**Required Modules:**
- ✅ `db` - conversations.getAll, messages.getAll for recent activity
- ✅ `vault` - readDirectory, readFile for file system operations
- ✅ `files` - showOpenDialog for file upload operations
- ✅ `events` - on, emit for real-time updates
- ✅ `intelligence` - read for master document content

### **UnifiedAPIClient Coverage Analysis**
**Available Modules for Card Overlay:**
- ✅ `db` - conversations.getAll, messages.getAll, addMessage
- ✅ `vault` - readDirectory, readFile, pathExists, copyFile
- ✅ `files` - showOpenDialog, processFile, indexFile
- ✅ `events` - on, emit, subscribe, unsubscribe for real-time updates
- ✅ `intelligence` - read, write for intelligence operations
- ✅ `settings` - get, set for user preferences

### **UnifiedAPIClient Implementation Examples**
```typescript
// ✅ COMPLIANT - All operations will use UnifiedAPIClient

// Database operations for recent activity
const recentConversations = await db.conversations.getAll()
const recentMessages = await db.messages.getAll(conversationId)

// Vault operations for file system
const recentFiles = await vault.readDirectory(contextPath)
const masterContent = await vault.readFile(masterPath)
const fileExists = await vault.pathExists(filePath)

// File operations for uploads
const uploadResult = await files.showOpenDialog({
  properties: ['openFile', 'multiSelections'],
  filters: [{ name: 'All Files', extensions: ['*'] }]
})

// Event operations for real-time updates
events.on('file:uploaded', handleFileUploaded)
events.on('intelligence:updated', handleIntelligenceUpdated)
events.emit('overlay:opened', { contextId: context.id })

// Intelligence operations for master content
const masterIntel = await intelligence.read(masterPath, vaultPath)
```

### **Migration Priority Matrix**
| Component | Legacy API Count | Migration Effort | Business Impact | Priority |
|-----------|------------------|------------------|-----------------|----------|
| Card Overlay (New) | 0 | N/A | HIGH | ✅ **READY** |
| Data Integration | 0 | N/A | HIGH | ✅ **READY** |
| Real-time Updates | 0 | N/A | MEDIUM | ✅ **READY** |
| File Operations | 0 | N/A | HIGH | ✅ **READY** |

---

## 📊 **CONTENT POPULATION STRATEGY**

### **Static Content:**
- Context name, type, description
- File count, chat count
- Last activity timestamp
- Context metadata

### **Dynamic Content:**
- Recent files (last 5 accessed)
- Recent chats (last 3 sessions)
- Intelligence processing status
- Activity metrics

### **Real-time Content:**
- Processing progress indicators
- New file notifications
- Chat activity updates
- Intelligence generation status

---

## 🚨 **IMPLEMENTATION PRIORITIES**

### **Phase 1: Core Overlay Structure (Week 1-2)**
- [ ] Create basic overlay component structure
- [ ] Implement hover/click trigger mechanisms
- [ ] Add basic content sections (header, summary, actions)
- [ ] Implement overlay positioning and styling

### **Phase 2: Content Integration (Week 3-4)**
- [x] Integrate with existing context data
- [x] Add activity summary population (conversations, files)
- [x] Implement quick action functionality (upload via files.showOpenDialog)
- [x] Add master mode button and navigation

### **Phase 3: Enhanced Features (Week 5-6)**
- [ ] Add red card highlighting integration
- [ ] Implement real-time content updates
- [ ] Add animation and transition effects
- [ ] Optimize performance and accessibility

### **Phase 4: Polish & Testing (Week 7-8)**
- [ ] User experience testing and refinement
- [ ] Performance optimization
- [ ] Accessibility improvements
- [ ] Cross-browser compatibility

### **Phase 5: UnifiedAPIClient Integration (ONGOING)**
- [x] **UnifiedAPIClient modules available** for all required operations
- [x] **Database operations** ready via `db.*` methods
- [x] **Vault operations** ready via `vault.*` methods
- [x] **File operations** ready via `files.*` methods
- [x] **Event operations** ready via `events.*` methods
- [x] **Intelligence operations** ready via `intelligence.*` methods

---

## 🎯 **UNIFIEDAPICLIENT INTEGRATION CHECKLIST**

### **Step 1: Import and Setup** ✅ **READY**
- [x] UnifiedAPIClient modules available for import
- [x] All required modules accessible (db, vault, files, events, intelligence)
- [x] No legacy API dependencies

### **Step 2: Data Integration** ✅ **READY**
- [x] Database operations available via `db.*` methods
- [x] Vault operations available via `vault.*` methods
- [x] File operations available via `files.*` methods
- [x] Event operations available via `events.*` methods

### **Step 3: Implementation** 🔄 **IN PROGRESS**
- [ ] Implement overlay component with UnifiedAPIClient calls
- [ ] Add real-time event subscription for updates
- [ ] Implement file upload functionality via `files.showOpenDialog`
- [ ] Add master mode navigation integration

### **Step 4: Testing & Validation** ❌ **PENDING**
- [ ] Test all UnifiedAPIClient operations end-to-end
- [ ] Validate real-time updates and event handling
- [ ] Test file operations and upload functionality
- [ ] Performance testing of overlay operations

---

## 📊 **COMPLIANCE SCORE**

| Category | Score | Status |
|----------|-------|---------|
| **UnifiedAPIClient Availability** | 5/5 | ✅ **100% READY** |
| **Module Coverage** | 5/5 | ✅ **100% READY** |
| **Data Integration** | 5/5 | ✅ **100% READY** |
| **Implementation Progress** | 2/5 | 🔄 **40% IN PROGRESS** |
| **Testing & Validation** | 0/5 | ❌ **0% PENDING** |

**Overall Compliance:** **60%** - UnifiedAPIClient fully ready, implementation in progress

---

## 🎯 **NEXT STEPS FOR FULL COMPLIANCE**

1. **Complete Overlay Component Implementation** (Priority 1 - HIGH)
2. **Integrate All UnifiedAPIClient Operations** (Priority 2 - HIGH)
3. **Implement Real-time Event Handling** (Priority 3 - MEDIUM)
4. **Add File Upload Functionality** (Priority 4 - MEDIUM)
5. **Complete Testing & Validation** (Priority 5 - LOW)

The HomePage card overlay system will provide users with a powerful yet intuitive way to assess context relevance and seamlessly transition to the full context vault experience, while maintaining the performance and usability standards expected in a modern application. **All UnifiedAPIClient modules are ready and available for immediate integration.**
