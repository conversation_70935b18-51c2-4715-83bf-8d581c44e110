/**
 * Simple Filesystem Module
 * Direct replacement for the current filesystem implementation
 * Handles all file operations, vault operations, and path utilities
 */

import { BaseAPIModule, ModuleConfig, ModuleDependency } from '../core/BaseAPIModule'

export class SimpleFilesystemModule extends BaseAPIModule {
  readonly name = 'filesystem'
  readonly version = '1.0.0'
  readonly description = 'Simple filesystem operations - direct replacement for current implementation'
  readonly dependencies: ModuleDependency[] = []

  private fileSystem: any
  private pathResolver: any

  protected async onInitialize(): Promise<void> {
    // Import required modules dynamically
    const { FileSystemManager } = await import('../../../fileSystem')
    const { PathResolver } = await import('../../../core/PathResolver')
    const { DatabaseManager } = await import('../../../database')

    // Create DatabaseManager instance for FileSystemManager
    const dbManager = new DatabaseManager()
    
    this.fileSystem = new FileSystemManager(dbManager)
    this.pathResolver = PathResolver

    this.log('info', 'Simple Filesystem Module initialized successfully')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering simple filesystem endpoints...')

    // Vault endpoints
    this.registerEndpoint('vault', 'createDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.mkdir(dirPath, { recursive: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!dirPath || typeof dirPath !== 'string') throw new Error('Invalid directory path')
        },
        description: 'Create directory recursively'
      }
    )

    this.registerEndpoint('vault', 'writeFile',
      async (filePath: string, content: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const normalizedPath = path.normalize(filePath)
          const dir = path.dirname(normalizedPath)
          await fs.promises.mkdir(dir, { recursive: true })
          
          const isBase64 = this.isBase64String(content)
          if (isBase64) {
            const buffer = Buffer.from(content, 'base64')
            await fs.promises.writeFile(normalizedPath, buffer)
          } else {
            await fs.promises.writeFile(normalizedPath, content, 'utf8')
          }
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, content: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (content === undefined || content === null) throw new Error('Invalid content')
        },
        description: 'Write file with content'
      }
    )

    this.registerEndpoint('vault', 'appendFile',
      async (filePath: string, content: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.appendFile(filePath, content, 'utf8')
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, content: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (content === undefined || content === null) throw new Error('Invalid content')
        },
        description: 'Append content to file'
      }
    )

    this.registerEndpoint('vault', 'readDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          const entries = await fs.promises.readdir(dirPath, { withFileTypes: true })
          const files = entries.map(entry => ({
            name: entry.name,
            isDirectory: entry.isDirectory(),
            isFile: entry.isFile()
          }))
          return { success: true, files }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!dirPath || typeof dirPath !== 'string') throw new Error('Invalid directory path')
        },
        description: 'Read directory contents'
      }
    )

    this.registerEndpoint('vault', 'removeDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.rmdir(dirPath, { recursive: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!dirPath || typeof dirPath !== 'string') throw new Error('Invalid directory path')
        },
        description: 'Remove directory recursively'
      }
    )

    this.registerEndpoint('vault', 'removeFile',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.unlink(filePath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Remove file'
      }
    )

    this.registerEndpoint('vault', 'scanFolder',
      async (folderPath: string) => {
        try {
          const result = await this.fileSystem.scanFolder(folderPath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (folderPath: string) => {
          if (!folderPath || typeof folderPath !== 'string') throw new Error('Invalid folder path')
        },
        description: 'Scan folder for files'
      }
    )

    this.registerEndpoint('vault', 'copyFile',
      async (source: string, destination: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.copyFile(source, destination)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (source: string, destination: string) => {
          if (!source || typeof source !== 'string') throw new Error('Invalid source path')
          if (!destination || typeof destination !== 'string') throw new Error('Invalid destination path')
        },
        description: 'Copy file from source to destination'
      }
    )

    this.registerEndpoint('vault', 'pathExists',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.access(filePath)
          return { success: true, exists: true }
        } catch {
          return { success: true, exists: false }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Check if path exists'
      }
    )

    this.registerEndpoint('vault', 'readFile',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          const content = await fs.promises.readFile(filePath, 'utf8')
          return { success: true, content }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Read file content as text'
      }
    )

    this.registerEndpoint('vault', 'readFileBase64',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          const buffer = await fs.promises.readFile(filePath)
          const content = buffer.toString('base64')
          return { success: true, content }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Read file content as base64'
      }
    )

    this.registerEndpoint('vault', 'saveVaultRegistry',
      async (registry: any) => {
        try {
          const result = await this.fileSystem.saveVaultRegistry(registry)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (registry: any) => {
          if (!registry || typeof registry !== 'object') throw new Error('Invalid registry object')
        },
        description: 'Save vault registry'
      }
    )

    this.registerEndpoint('vault', 'initializeVaultRoot',
      async (rootPath: string) => {
        try {
          const result = await this.fileSystem.initializeVaultRoot(rootPath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (rootPath: string) => {
          if (!rootPath || typeof rootPath !== 'string') throw new Error('Invalid root path')
        },
        description: 'Initialize vault root'
      }
    )

    this.registerEndpoint('vault', 'scanContexts',
      async (vaultRoot: string) => {
        try {
          const result = await this.fileSystem.scanContexts(vaultRoot)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (vaultRoot: string) => {
          if (!vaultRoot || typeof vaultRoot !== 'string') throw new Error('Invalid vault root')
        },
        description: 'Scan vault contexts'
      }
    )

    // Files endpoints
    this.registerEndpoint('files', 'getVaultRootPath',
      () => {
        try {
          const path = this.fileSystem.getVaultRootPath()
          return { success: true, path }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      { description: 'Get vault root path' }
    )

    this.registerEndpoint('files', 'setVaultRootPath',
      async (path: string) => {
        try {
          const result = await this.fileSystem.setVaultRootPath(path)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (path: string) => {
          if (!path || typeof path !== 'string') throw new Error('Invalid path')
        },
        description: 'Set vault root path'
      }
    )

    this.registerEndpoint('files', 'getChatloFolderPath',
      () => {
        try {
          const path = this.fileSystem.getChatloFolderPath()
          return { success: true, path }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      { description: 'Get Chatlo folder path' }
    )

    this.registerEndpoint('files', 'setChatloFolderPath',
      async (path: string) => {
        try {
          const result = await this.fileSystem.setChatloFolderPath(path)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (path: string) => {
          if (!path || typeof path !== 'string') throw new Error('Invalid path')
        },
        description: 'Set Chatlo folder path'
      }
    )

    this.registerEndpoint('files', 'getIndexedFiles',
      async () => {
        try {
          const files = await this.fileSystem.getIndexedFiles()
          return { success: true, files }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      { description: 'Get indexed files' }
    )

    this.registerEndpoint('files', 'getFileProcessorPlugins',
      async () => {
        try {
          const plugins = await this.fileSystem.getFileProcessorPlugins()
          return { success: true, plugins }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      { description: 'Get file processor plugins' }
    )

    this.registerEndpoint('files', 'setFileProcessorPluginEnabled',
      async (pluginName: string, enabled: boolean) => {
        try {
          const result = await this.fileSystem.setFileProcessorPluginEnabled(pluginName, enabled)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginName: string, enabled: boolean) => {
          if (!pluginName || typeof pluginName !== 'string') throw new Error('Invalid plugin name')
          if (typeof enabled !== 'boolean') throw new Error('Invalid enabled state')
        },
        description: 'Set file processor plugin enabled state'
      }
    )

    this.registerEndpoint('files', 'searchFiles',
      async (query: string) => {
        try {
          const results = await this.fileSystem.searchFiles(query)
          return { success: true, results }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (query: string) => {
          if (!query || typeof query !== 'string') throw new Error('Invalid search query')
        },
        description: 'Search files'
      }
    )

    this.registerEndpoint('files', 'getMetadata',
      async (filePath: string) => {
        try {
          const metadata = await this.fileSystem.getMetadata(filePath)
          return { success: true, metadata }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Get file metadata'
      }
    )

    this.registerEndpoint('files', 'reindexTree',
      async (rootPath: string) => {
        try {
          const result = await this.fileSystem.reindexTree(rootPath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (rootPath: string) => {
          if (!rootPath || typeof rootPath !== 'string') throw new Error('Invalid root path')
        },
        description: 'Reindex file tree'
      }
    )

    this.registerEndpoint('files', 'processFileContent',
      async (filePath: string, options: any) => {
        try {
          const result = await this.fileSystem.processFileContent(filePath, options)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, options: any) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (!options || typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Process file content'
      }
    )

    this.registerEndpoint('files', 'indexFile',
      async (filePath: string) => {
        try {
          const result = await this.fileSystem.indexFile(filePath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Index file'
      }
    )

    this.registerEndpoint('files', 'indexVaultFile',
      async (filePath: string) => {
        try {
          const result = await this.fileSystem.indexVaultFile(filePath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Index vault file'
      }
    )

    this.registerEndpoint('files', 'indexAllFiles',
      async (rootPath: string) => {
        try {
          const result = await this.fileSystem.indexAllFiles(rootPath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (rootPath: string) => {
          if (!rootPath || typeof rootPath !== 'string') throw new Error('Invalid root path')
        },
        description: 'Index all files'
      }
    )

    this.registerEndpoint('files', 'copyFileToUploads',
      async (sourcePath: string, fileName: string) => {
        try {
          const result = await this.fileSystem.copyFileToUploads(sourcePath, fileName)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (sourcePath: string, fileName: string) => {
          if (!sourcePath || typeof sourcePath !== 'string') throw new Error('Invalid source path')
          if (!fileName || typeof fileName !== 'string') throw new Error('Invalid file name')
        },
        description: 'Copy file to uploads'
      }
    )

    this.registerEndpoint('files', 'saveContentToVault',
      async (content: string, fileName: string, contextPath: string) => {
        try {
          const result = await this.fileSystem.saveContentToVault(content, fileName, contextPath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (content: string, fileName: string, contextPath: string) => {
          if (content === undefined || content === null) throw new Error('Invalid content')
          if (!fileName || typeof fileName !== 'string') throw new Error('Invalid file name')
          if (!contextPath || typeof contextPath !== 'string') throw new Error('Invalid context path')
        },
        description: 'Save content to vault'
      }
    )

    this.registerEndpoint('files', 'saveContentAsFile',
      async (content: string, filePath: string) => {
        try {
          const result = await this.fileSystem.saveContentAsFile(content, filePath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (content: string, filePath: string) => {
          if (content === undefined || content === null) throw new Error('Invalid content')
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Save content as file'
      }
    )

    this.registerEndpoint('files', 'addFileAttachment',
      async (messageId: string, filePath: string, metadata: any) => {
        try {
          const result = await this.fileSystem.addFileAttachment(messageId, filePath, metadata)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (messageId: string, filePath: string, metadata: any) => {
          if (!messageId || typeof messageId !== 'string') throw new Error('Invalid message ID')
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (!metadata || typeof metadata !== 'object') throw new Error('Invalid metadata')
        },
        description: 'Add file attachment to message'
      }
    )

    this.registerEndpoint('files', 'getFileAttachments',
      async (messageId: string) => {
        try {
          const attachments = await this.fileSystem.getFileAttachments(messageId)
          return { success: true, attachments }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (messageId: string) => {
          if (!messageId || typeof messageId !== 'string') throw new Error('Invalid message ID')
        },
        description: 'Get file attachments for message'
      }
    )

    this.registerEndpoint('files', 'getMessageFiles',
      async (messageId: string) => {
        try {
          const files = await this.fileSystem.getMessageFiles(messageId)
          return { success: true, files }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (messageId: string) => {
          if (!messageId || typeof messageId !== 'string') throw new Error('Invalid message ID')
        },
        description: 'Get files for message'
      }
    )

    this.registerEndpoint('files', 'removeFileAttachment',
      async (attachmentId: string) => {
        try {
          const result = await this.fileSystem.removeFileAttachment(attachmentId)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (attachmentId: string) => {
          if (!attachmentId || typeof attachmentId !== 'string') throw new Error('Invalid attachment ID')
        },
        description: 'Remove file attachment'
      }
    )

    this.registerEndpoint('files', 'deleteFile',
      async (filePath: string) => {
        try {
          const result = await this.fileSystem.deleteFile(filePath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Delete file'
      }
    )

    this.registerEndpoint('files', 'showOpenDialog',
      async (options: any) => {
        try {
          this.log('info', `[DIALOG] showOpenDialog called with options: ${JSON.stringify(options)}`)
          const { dialog } = await import('electron')
          const result = await dialog.showOpenDialog(options)
          this.log('info', `[DIALOG] showOpenDialog result: ${JSON.stringify({ canceled: result.canceled, filePathsCount: result.filePaths?.length || 0 })}`)
          return { success: true, ...result }
        } catch (error: any) {
          this.log('error', `[DIALOG] showOpenDialog error: ${error?.message}`)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (options: any) => {
          if (!options || typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Show open dialog'
      }
    )

    this.registerEndpoint('files', 'showSaveDialog',
      async (options: any) => {
        try {
          this.log('info', `[DIALOG] showSaveDialog called with options: ${JSON.stringify(options)}`)
          const { dialog } = await import('electron')
          const result = await dialog.showSaveDialog(options)
          this.log('info', `[DIALOG] showSaveDialog result: ${JSON.stringify({ canceled: result.canceled, filePath: (result as any).filePath || null })}`)
          return { success: true, ...result }
        } catch (error: any) {
          this.log('error', `[DIALOG] showSaveDialog error: ${error?.message}`)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (options: any) => {
          if (!options || typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Show save dialog'
      }
    )

    this.registerEndpoint('files', 'openPDFViewer',
      async (filePath: string) => {
        try {
          const result = await this.fileSystem.openPDFViewer(filePath)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Open PDF viewer'
      }
    )

    this.registerEndpoint('files', 'processFile',
      async (filePath: string, options?: any) => {
        try {
          const result = await this.fileSystem.processFile(filePath, options)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, options?: any) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Process file'
      }
    )

    this.registerEndpoint('files', 'getFileContent',
      async (filePath: string, options?: any) => {
        try {
          const result = await this.fileSystem.getFileContent(filePath, options)
          return { success: true, ...result }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, options?: any) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Get file content'
      }
    )

    // Path utilities
    this.registerEndpoint('path', 'normalize',
      (filePath: string) => {
        try {
          // FIXED: Call static method correctly
          const normalized = this.pathResolver.normalizePath(filePath)
          return { success: true, path: normalized }
        } catch (error: any) {
          this.log('error', `Path normalize failed: ${error.message}`, { filePath })
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Normalize file path'
      }
    )

    this.registerEndpoint('path', 'join',
      (...paths: string[]) => {
        try {
          // FIXED: Use correct static method name 'joinSafe'
          const joined = this.pathResolver.joinSafe(...paths)
          return { success: true, path: joined }
        } catch (error: any) {
          this.log('error', `Path join failed: ${error.message}`, { paths })
          return { success: false, error: error.message }
        }
      },
      {
        validator: (...paths: string[]) => {
          if (!paths.length || paths.some(p => !p || typeof p !== 'string')) {
            throw new Error('Invalid paths')
          }
        },
        description: 'Join path segments'
      }
    )

    this.registerEndpoint('path', 'getContextDirs',
      (vaultRoot: string) => {
        try {
          const dirs = this.pathResolver.getContextDirs(vaultRoot)
          return { success: true, dirs }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (vaultRoot: string) => {
          if (!vaultRoot || typeof vaultRoot !== 'string') throw new Error('Invalid vault root')
        },
        description: 'Get context directories'
      }
    )

    // Shell operations
    this.registerEndpoint('shell', 'openPath',
      async (filePath: string) => {
        const { shell } = await import('electron')
        try {
          await shell.openPath(filePath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Open path in default application'
      }
    )

    this.registerEndpoint('shell', 'showItemInFolder',
      async (filePath: string) => {
        const { shell } = await import('electron')
        try {
          await shell.showItemInFolder(filePath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!filePath || typeof filePath !== 'string') throw new Error('Invalid file path')
        },
        description: 'Show item in folder'
      }
    )

    this.log('info', `Registered ${this.endpoints.size} simple filesystem endpoints`)
  }

  private isBase64String(str: string): boolean {
    try {
      return Buffer.from(str, 'base64').toString('base64') === str
    } catch {
      return false
    }
  }
}
