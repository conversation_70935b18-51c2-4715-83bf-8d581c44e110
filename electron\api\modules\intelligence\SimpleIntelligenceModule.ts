/**
 * Simple Intelligence Module
 * Direct replacement for the current intelligence implementation
 * No external dependencies - works with existing system
 */

import { BaseAPIModule, ModuleConfig, ModuleDependency } from '../core/BaseAPIModule'

export class SimpleIntelligenceModule extends BaseAPIModule {
  readonly name = 'intelligence'
  readonly version = '1.0.0'
  readonly description = 'Simple intelligence operations - direct replacement for current implementation'
  readonly dependencies: ModuleDependency[] = []

  private intelCore: any
  private fileSystem: any
  private PathResolver: any

  protected async onInitialize(): Promise<void> {
    // Import required modules dynamically
    const { IntelligenceCoreService } = await import('../../../core/IntelligenceCoreService')
    const { FileCoreService } = await import('../../../core/FileCoreService')
    const { PathResolver } = await import('../../../core/PathResolver')
    
    this.intelCore = new IntelligenceCoreService()
    this.fileSystem = new FileCoreService()
    this.PathResolver = PathResolver
    
    this.log('info', 'Simple Intelligence Module initialized successfully')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering simple intelligence endpoints...')

    // Main intelligence:write endpoint (the big one!)
    this.registerEndpoint('intelligence', 'write',
      async (filePath: string, vaultPath: string, payload: any) => {
        try {
          // [IPC-LOG] Log intelligence write calls
          console.log('[SIMPLE-INTEL] 🧠 intelligence:write called:', {
            filePath,
            vaultPath,
            payloadType: typeof payload,
            payloadKeys: payload ? Object.keys(payload) : [],
            isContextNotes: filePath.startsWith('context-notes/'),
            timestamp: new Date().toISOString()
          });

          // SECURITY ENHANCEMENT: Get allowed vault roots and validate
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          
          // SECURITY CHECK: Prevent codebase contamination
          if (this.PathResolver.isCodebasePath(filePath)) {
            console.error('[SIMPLE-INTEL] 🚨 Rejecting codebase path in intelligence:write:', filePath)
            return { success: false, error: 'Security violation: Cannot write to codebase paths' }
          }
          
          // SECURITY CHECK: Validate vault path boundaries
          if (vaultPath && !this.PathResolver.validateVaultPath(vaultPath, allowedRoots)) {
            console.error('[SIMPLE-INTEL] 🚨 Vault path outside allowed boundaries:', vaultPath)
            console.error('[SIMPLE-INTEL] 🚨 This might be a context ID instead of a full vault path')
            console.error('[SIMPLE-INTEL] 🚨 Expected format: C:\\Users\\<USER>\\vault-name\\context-name')
            console.error('[SIMPLE-INTEL] 🚨 Received: ', vaultPath)
            return { success: false, error: `Security violation: Invalid vault path format. Expected full vault path, got: ${vaultPath}` }
          }
          
          // SECURITY ENHANCEMENT: Comprehensive path sanitization and validation
          const sanitizedFilePath = this.PathResolver.sanitizeAndValidatePath(filePath, allowedRoots)
          if (!sanitizedFilePath) {
            console.error('[SIMPLE-INTEL] 🚨 Path sanitization failed for:', filePath)
            return { success: false, error: 'Security violation: Invalid or malicious file path' }
          }
          
          // SECURITY ENHANCEMENT: Validate intelligence data before storage
          if (payload && !this.PathResolver.validateIntelligenceData(payload)) {
            console.error('[SIMPLE-INTEL] 🚨 Intelligence data validation failed')
            return { success: false, error: 'Security violation: Invalid intelligence data format' }
          }
          
          // SECURITY ENHANCEMENT: Validate context ID if provided
          if (payload?.storage_metadata?.context_path && 
              !this.PathResolver.validateContextId(payload.storage_metadata.context_path)) {
            console.error('[SIMPLE-INTEL] 🚨 Invalid context path in storage metadata:', payload.storage_metadata.context_path)
            return { success: false, error: 'Security violation: Invalid context identifier' }
          }
          
          const inferredVault = this.PathResolver.inferVaultPath(sanitizedFilePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          
          // SECURITY CHECK: Validate inferred vault path
          if (inferredVault && !this.PathResolver.validateVaultPath(inferredVault, allowedRoots)) {
            console.error('[SIMPLE-INTEL] 🚨 Inferred vault path outside allowed boundaries:', inferredVault)
            console.error('[SIMPLE-INTEL] 🚨 This might be a context ID instead of a full vault path')
            console.error('[SIMPLE-INTEL] 🚨 Expected format: C:\\Users\\<USER>\\vault-name\\context-name')
            console.error('[SIMPLE-INTEL] 🚨 Received: ', inferredVault)
            return { success: false, error: `Security violation: Inferred vault path outside allowed boundaries. Expected full vault path, got: ${inferredVault}` }
          }
          
          console.log('[SIMPLE-INTEL] 🧠 Vault path resolution:', {
            originalVaultPath: vaultPath,
            sanitizedFilePath,
            inferredVault,
            allowedRoots,
            vaultRootPath: this.fileSystem.getVaultRootPath()
          });
          
          if (!inferredVault) {
            console.error('[SIMPLE-INTEL] 🚨 Could not infer valid vault path for:', sanitizedFilePath)
            return { success: false, error: 'Security violation: Unable to infer valid vault path' }
          }
          
          // SECURITY ENHANCEMENT: Use sanitized file path for storage
          const result = await this.intelCore.save(sanitizedFilePath, inferredVault, payload)
          
          console.log('[SIMPLE-INTEL] 🧠 intelligence:write result:', {
            filePath,
            success: result.success,
            error: result.error,
            timestamp: new Date().toISOString()
          });
          
          return result
        } catch (error: any) { 
          console.log('[SIMPLE-INTEL] ❌ intelligence:write error:', {
            filePath,
            vaultPath,
            error: error.message,
            timestamp: new Date().toISOString()
          });
          return { success: false, error: error.message } 
        }
      },
      {
        validator: (filePath: string, vaultPath: string, _payload: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Write intelligence data (canonical JSON, optional markdown) to context storage (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:read endpoint
    this.registerEndpoint('intelligence', 'read',
      async (filePath: string, vaultPath: string) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.get(filePath, inferredVault)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Read intelligence data; if only markdown exists, stub JSON is created (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:save endpoint
    this.registerEndpoint('intelligence', 'save',
      async (filePath: string, vaultPath: string, payload: any) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.save(filePath, inferredVault, payload)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string, _payload: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Save intelligence data with dual storage (.json canonical + .md optional) (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:get endpoint
    this.registerEndpoint('intelligence', 'get',
      async (filePath: string, vaultPath: string) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.get(filePath, inferredVault)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Get intelligence data (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:analyze endpoint
    this.registerEndpoint('intelligence', 'analyze',
      async (filePath: string, vaultPath: string, options: any) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath(filePath, { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.analyze(filePath, inferredVault, options)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (filePath: string, vaultPath: string, options: any) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (options && typeof options !== 'object') throw new Error('Invalid options')
        },
        description: 'Analyze file content and generate intelligence (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:listSessions endpoint
    this.registerEndpoint('intelligence', 'listSessions',
      async (vaultPath: string) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath('', { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.listSessions(inferredVault)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (vaultPath: string) => {
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'List intelligence sessions for a vault (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    // intelligence:writeSession endpoint
    this.registerEndpoint('intelligence', 'writeSession',
      async (sessionId: string, vaultPath: string, payload: any) => {
        try {
          const allowedRoots = [this.fileSystem.getVaultRootPath()].filter(Boolean)
          const inferredVault = this.PathResolver.inferVaultPath('', { allowedVaultRoots: allowedRoots }) || vaultPath
          if (!inferredVault) throw new Error('Unable to infer vault path')
          return await this.intelCore.writeSession(sessionId, inferredVault, payload)
        } catch (error: any) { return { success: false, error: error.message } }
      },
      {
        validator: (sessionId: string, vaultPath: string, payload: any) => {
          if (!this.validateInput(sessionId, 'string', 100)) throw new Error('Invalid session ID')
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
          if (!payload || typeof payload !== 'object') throw new Error('Invalid payload')
        },
        description: 'Write session data to intelligence storage (SimpleIntelligenceModule)',
        requiresAuth: process.env.NODE_ENV === 'production'
      }
    )

    this.log('info', `Registered ${this.endpoints.size} simple intelligence endpoints`)
  }
}
