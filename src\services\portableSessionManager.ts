/**
 * Portable Session Manager
 * Handles session state persistence across portable and local mode switches
 * 
 * WEEK 2: Session & State Persistence Implementation
 */

import { useSessionStore, VaultInfo, ContextInfo } from '../store/sessionStore'
import { cacheManager } from './cacheManager'

export interface SessionPersistence {
  portableModeEnabled: boolean
  lastKnownVaultPath: string
  lastKnownContextId: string
  sessionTimestamp: Date
  modeSwitchCount: number
  userPreferences: {
    theme: 'light' | 'dark' | 'system'
    layout: 'compact' | 'comfortable'
    autoSave: boolean
    notifications: boolean
  }
  activityPatterns: {
    lastActivity: Date
    vaultUsageHistory: string[]
    contextSelectionHistory: string[]
    workflowPreferences: Record<string, any>
  }
}

export interface PortableModeTransition {
  fromMode: 'local' | 'portable'
  toMode: 'local' | 'portable'
  timestamp: Date
  sessionState: SessionPersistence
  success: boolean
  error?: string
}

class PortableSessionManager {
  private static instance: PortableSessionManager
  private transitionHistory: PortableModeTransition[] = []
  private readonly STORAGE_KEY = 'chatlo_portable_session_persistence'
  private readonly MAX_HISTORY = 10

  static getInstance(): PortableSessionManager {
    if (!PortableSessionManager.instance) {
      PortableSessionManager.instance = new PortableSessionManager()
    }
    return PortableSessionManager.instance
  }

  /**
   * Save current session state for portable mode transition
   */
  async saveSessionState(): Promise<SessionPersistence> {
    try {
      console.log('[PORTABLE-SESSION] 💾 Saving current session state...')
      
      const store = useSessionStore.getState()
      const currentVault = store.getCurrentVault()
      const currentContext = store.getCurrentContext()
      
      // Get current portable mode status
      const portableModeEnabled = await this.getPortableModeStatus()
      
      // Create session persistence object
      const sessionState: SessionPersistence = {
        portableModeEnabled,
        lastKnownVaultPath: currentVault?.path || '',
        lastKnownContextId: currentContext?.id || '',
        sessionTimestamp: new Date(),
        modeSwitchCount: await this.getModeSwitchCount(),
        userPreferences: store.userPreferences,
        activityPatterns: {
          lastActivity: store.lastActivity,
          vaultUsageHistory: await this.getVaultUsageHistory(),
          contextSelectionHistory: await this.getContextSelectionHistory(),
          workflowPreferences: await this.getWorkflowPreferences()
        }
      }

      // Save to localStorage for cross-session persistence
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessionState))
      
      // Also save to cache for immediate access
      await cacheManager.set('portable_session_state', sessionState, 24 * 60 * 60 * 1000) // 24 hours
      
      console.log('[PORTABLE-SESSION] ✅ Session state saved successfully')
      return sessionState
    } catch (error) {
      console.error('[PORTABLE-SESSION] ❌ Failed to save session state:', error)
      throw error
    }
  }

  /**
   * Restore session state after portable mode transition
   */
  async restoreSessionState(): Promise<{ success: boolean; restoredContext?: { vault: VaultInfo; context: ContextInfo }; error?: string }> {
    try {
      console.log('[PORTABLE-SESSION] 🔄 Restoring session state...')
      
      // Try to get session state from cache first
      let sessionState = await cacheManager.get<SessionPersistence>('portable_session_state')
      
      // Fall back to localStorage if cache miss
      if (!sessionState) {
        const stored = localStorage.getItem(this.STORAGE_KEY)
        if (stored) {
          sessionState = JSON.parse(stored)
          console.log('[PORTABLE-SESSION] 📂 Retrieved session state from localStorage')
        }
      }

      if (!sessionState) {
        console.log('[PORTABLE-SESSION] ℹ️ No session state to restore')
        return { success: true }
      }

      // Validate session state
      if (!this.validateSessionState(sessionState)) {
        console.warn('[PORTABLE-SESSION] ⚠️ Invalid session state, skipping restoration')
        return { success: true }
      }

      // Attempt to restore vault and context
      const restoredContext = await this.restoreVaultAndContext(sessionState)
      
      if (restoredContext) {
        // Update session store
        const store = useSessionStore.getState()
        store.setCurrentContext(restoredContext.vault, restoredContext.context)
        
        // Restore user preferences
        store.updateUserPreferences(sessionState.userPreferences)
        
        console.log('[PORTABLE-SESSION] ✅ Session state restored successfully')
        return { success: true, restoredContext }
      } else {
        console.log('[PORTABLE-SESSION] ℹ️ Could not restore context, but session state preserved')
        return { success: true }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      console.error('[PORTABLE-SESSION] ❌ Failed to restore session state:', errorMsg)
      return { success: false, error: errorMsg }
    }
  }

  /**
   * Handle transition from local to portable mode
   */
  async transitionToPortableMode(): Promise<PortableModeTransition> {
    try {
      console.log('[PORTABLE-SESSION] 🔌 Transitioning to portable mode...')
      
      // Save current session state
      const sessionState = await this.saveSessionState()
      
      // Update portable mode status
      sessionState.portableModeEnabled = true
      
      // Create transition record
      const transition: PortableModeTransition = {
        fromMode: 'local',
        toMode: 'portable',
        timestamp: new Date(),
        sessionState,
        success: true
      }
      
      // Record transition
      this.recordTransition(transition)
      
      console.log('[PORTABLE-SESSION] ✅ Successfully transitioned to portable mode')
      return transition
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      console.error('[PORTABLE-SESSION] ❌ Failed to transition to portable mode:', errorMsg)
      
      const transition: PortableModeTransition = {
        fromMode: 'local',
        toMode: 'portable',
        timestamp: new Date(),
        sessionState: await this.createFallbackSessionState(),
        success: false,
        error: errorMsg
      }
      
      this.recordTransition(transition)
      return transition
    }
  }

  /**
   * Handle transition from portable to local mode
   */
  async transitionToLocalMode(): Promise<PortableModeTransition> {
    try {
      console.log('[PORTABLE-SESSION] 💻 Transitioning to local mode...')
      
      // Save current session state
      const sessionState = await this.saveSessionState()
      
      // Update portable mode status
      sessionState.portableModeEnabled = false
      
      // Create transition record
      const transition: PortableModeTransition = {
        fromMode: 'portable',
        toMode: 'local',
        timestamp: new Date(),
        sessionState,
        success: true
      }
      
      // Record transition
      this.recordTransition(transition)
      
      console.log('[PORTABLE-SESSION] ✅ Successfully transitioned to local mode')
      return transition
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      console.error('[PORTABLE-SESSION] ❌ Failed to transition to local mode:', errorMsg)
      
      const transition: PortableModeTransition = {
        fromMode: 'portable',
        toMode: 'local',
        timestamp: new Date(),
        sessionState: await this.createFallbackSessionState(),
        success: false,
        error: errorMsg
      }
      
      this.recordTransition(transition)
      return transition
    }
  }

  /**
   * Get transition history for debugging and analysis
   */
  getTransitionHistory(): PortableModeTransition[] {
    return [...this.transitionHistory]
  }

  /**
   * Clear transition history
   */
  clearTransitionHistory(): void {
    this.transitionHistory = []
    console.log('[PORTABLE-SESSION] 🧹 Transition history cleared')
  }

  /**
   * Get current portable mode status
   */
  private async getPortableModeStatus(): Promise<boolean> {
    try {
      if (window.electronAPI?.settings) {
        const status = await window.electronAPI.settings.get('portable-mode-enabled')
        return status === true || status === 'true'
      }
      return false
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to get portable mode status:', error)
      return false
    }
  }

  /**
   * Get mode switch count from settings
   */
  private async getModeSwitchCount(): Promise<number> {
    try {
      if (window.electronAPI?.settings) {
        const count = await window.electronAPI.settings.get('mode-switch-count')
        return typeof count === 'number' ? count : 0
      }
      return 0
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to get mode switch count:', error)
      return 0
    }
  }

  /**
   * Get vault usage history from cache
   */
  private async getVaultUsageHistory(): Promise<string[]> {
    try {
      const history = await cacheManager.get<string[]>('vault_usage_history')
      return history || []
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to get vault usage history:', error)
      return []
    }
  }

  /**
   * Get context selection history from cache
   */
  private async getContextSelectionHistory(): Promise<string[]> {
    try {
      const history = await cacheManager.get<string[]>('context_selection_history')
      return history || []
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to get context selection history:', error)
      return []
    }
  }

  /**
   * Get workflow preferences from cache
   */
  private async getWorkflowPreferences(): Promise<Record<string, any>> {
    try {
      const prefs = await cacheManager.get<Record<string, any>>('workflow_preferences')
      return prefs || {}
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to get workflow preferences:', error)
      return {}
    }
  }

  /**
   * Validate session state before restoration
   */
  private validateSessionState(sessionState: SessionPersistence): boolean {
    try {
      // Basic validation
      if (!sessionState || typeof sessionState !== 'object') return false
      if (typeof sessionState.portableModeEnabled !== 'boolean') return false
      if (typeof sessionState.lastKnownVaultPath !== 'string') return false
      if (typeof sessionState.lastKnownContextId !== 'string') return false
      if (!(sessionState.sessionTimestamp instanceof Date)) return false
      
      return true
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Session state validation failed:', error)
      return false
    }
  }

  /**
   * Attempt to restore vault and context from session state
   */
  private async restoreVaultAndContext(sessionState: SessionPersistence): Promise<{ vault: VaultInfo; context: ContextInfo } | null> {
    try {
      // This would need to be implemented based on your vault service
      // For now, return null to indicate restoration not possible
      console.log('[PORTABLE-SESSION] 🔍 Attempting to restore vault and context...')
      
      // TODO: Implement actual vault and context restoration
      // This would involve:
      // 1. Checking if the vault path still exists
      // 2. Validating the context ID is still valid
      // 3. Creating VaultInfo and ContextInfo objects
      
      return null
    } catch (error) {
      console.warn('[PORTABLE-SESSION] Failed to restore vault and context:', error)
      return null
    }
  }

  /**
   * Create fallback session state when transition fails
   */
  private async createFallbackSessionState(): Promise<SessionPersistence> {
    const store = useSessionStore.getState()
    
    return {
      portableModeEnabled: false,
      lastKnownVaultPath: '',
      lastKnownContextId: '',
      sessionTimestamp: new Date(),
      modeSwitchCount: 0,
      userPreferences: store.userPreferences,
      activityPatterns: {
        lastActivity: new Date(),
        vaultUsageHistory: [],
        contextSelectionHistory: [],
        workflowPreferences: {}
      }
    }
  }

  /**
   * Record a mode transition for history
   */
  private recordTransition(transition: PortableModeTransition): void {
    this.transitionHistory.push(transition)
    
    // Keep only the last MAX_HISTORY transitions
    if (this.transitionHistory.length > this.MAX_HISTORY) {
      this.transitionHistory = this.transitionHistory.slice(-this.MAX_HISTORY)
    }
    
    console.log('[PORTABLE-SESSION] 📝 Recorded transition:', {
      from: transition.fromMode,
      to: transition.toMode,
      success: transition.success,
      timestamp: transition.timestamp
    })
  }
}

// Export singleton instance
export const portableSessionManager = PortableSessionManager.getInstance()

// Export default for direct import
export default portableSessionManager
