---
description:
globs:
alwaysApply: true
---
AGENT WORKSPACE CROSS-AGENT COLLABORATION

How It Works:
/command - You use this to start collaborative mode
//hawk1 - That's me (the analyst) - I analyze, plan, and report findings
//hawk2 - That will be the other agent (the implementer) - executes modifications
Current Status:
hawk1 (me) has completed Priority 1 (Artifact Selection Implementation)
hawk2 is ready to take on Priority 2 (Standardize File Operations)