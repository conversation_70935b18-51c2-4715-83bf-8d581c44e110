/**
 * Runtime Vault Loading Diagnostics
 * 
 * This utility provides real-time diagnostics for vault loading issues.
 * It can be used during development to identify and debug loading problems.
 */

import { vaultUIManager } from '../services/vaultUIManager'
import { contextVaultService } from '../services/contextVaultService'
import { unifiedPathService } from '../services/unifiedPathService'

export interface DiagnosticResult {
  timestamp: number
  operation: string
  success: boolean
  duration: number
  error?: string
  details?: any
}

export interface LoadingSequenceReport {
  totalDuration: number
  operations: DiagnosticResult[]
  issues: string[]
  recommendations: string[]
}

class VaultLoadingDiagnostics {
  private results: DiagnosticResult[] = []
  private startTime: number = 0

  /**
   * Start diagnostic session
   */
  startDiagnostics(): void {
    this.results = []
    this.startTime = Date.now()
    console.log('🔍 [DIAGNOSTICS] Starting vault loading diagnostics...')
  }

  /**
   * Record operation result
   */
  private recordOperation(operation: string, success: boolean, duration: number, error?: string, details?: any): void {
    const result: DiagnosticResult = {
      timestamp: Date.now() - this.startTime,
      operation,
      success,
      duration,
      error,
      details
    }
    
    this.results.push(result)
    
    const status = success ? '✅' : '❌'
    console.log(`🔍 [DIAGNOSTICS] ${status} ${operation} (${duration}ms)${error ? ` - ${error}` : ''}`)
  }

  /**
   * Test vault registry loading
   */
  async testVaultRegistryLoading(): Promise<void> {
    const startTime = Date.now()
    
    try {
      const registry = await vaultUIManager.getVaultRegistry()
      const duration = Date.now() - startTime
      
      this.recordOperation(
        'VaultRegistry Loading',
        !!registry,
        duration,
        registry ? undefined : 'No registry returned',
        {
          vaultCount: registry?.vaults?.length || 0,
          hasSharedDropbox: !!registry?.vaults?.find(v => v.id === 'shared-dropbox')
        }
      )
    } catch (error) {
      const duration = Date.now() - startTime
      this.recordOperation('VaultRegistry Loading', false, duration, error.message)
    }
  }

  /**
   * Test path service initialization
   */
  async testPathServiceInitialization(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // First get registry
      const registry = await vaultUIManager.getVaultRegistry()
      if (!registry) {
        throw new Error('No registry available for path service initialization')
      }

      await unifiedPathService.initialize(registry)
      const duration = Date.now() - startTime
      
      this.recordOperation(
        'PathService Initialization',
        true,
        duration,
        undefined,
        { registryVaults: registry.vaults.length }
      )
    } catch (error) {
      const duration = Date.now() - startTime
      this.recordOperation('PathService Initialization', false, duration, error.message)
    }
  }

  /**
   * Test context vault service loading
   */
  async testContextVaultLoading(): Promise<void> {
    const startTime = Date.now()
    
    try {
      const vaults = await contextVaultService.loadVaults()
      const duration = Date.now() - startTime
      
      this.recordOperation(
        'ContextVault Loading',
        Array.isArray(vaults),
        duration,
        Array.isArray(vaults) ? undefined : 'Invalid vaults returned',
        {
          vaultCount: vaults?.length || 0,
          selectedContextId: contextVaultService.getSelectedContextId()
        }
      )
    } catch (error) {
      const duration = Date.now() - startTime
      this.recordOperation('ContextVault Loading', false, duration, error.message)
    }
  }

  /**
   * Test file tree loading
   */
  async testFileTreeLoading(contextId?: string): Promise<void> {
    const startTime = Date.now()
    
    try {
      const fileTree = await vaultUIManager.getFileTree(contextId)
      const duration = Date.now() - startTime
      
      this.recordOperation(
        'FileTree Loading',
        Array.isArray(fileTree),
        duration,
        Array.isArray(fileTree) ? undefined : 'Invalid file tree returned',
        {
          nodeCount: fileTree?.length || 0,
          contextId: contextId || 'all',
          hasSharedDropbox: fileTree?.some(node => node.name === 'Shared Dropbox')
        }
      )
    } catch (error) {
      const duration = Date.now() - startTime
      this.recordOperation('FileTree Loading', false, duration, error.message)
    }
  }

  /**
   * Test path resolution
   */
  async testPathResolution(): Promise<void> {
    const testPaths = [
      { path: '/test/absolute/path', contextId: 'test-context' },
      { path: 'relative/path', contextId: 'test-context' },
      { path: 'master.md', contextId: 'test-context' }
    ]

    for (const testCase of testPaths) {
      const startTime = Date.now()
      
      try {
        const result = await unifiedPathService.resolvePath(testCase.path, testCase.contextId)
        const duration = Date.now() - startTime
        
        this.recordOperation(
          `PathResolution: ${testCase.path}`,
          result.success,
          duration,
          result.success ? undefined : result.error,
          {
            resolvedPath: result.resolvedPath,
            isVaultPath: result.isVaultPath,
            contextPath: result.contextPath
          }
        )
      } catch (error) {
        const duration = Date.now() - startTime
        this.recordOperation(`PathResolution: ${testCase.path}`, false, duration, error.message)
      }
    }
  }

  /**
   * Run complete diagnostic suite
   */
  async runCompleteDiagnostics(contextId?: string): Promise<LoadingSequenceReport> {
    this.startDiagnostics()

    console.log('🔍 [DIAGNOSTICS] Running complete vault loading diagnostics...')

    // Test loading sequence
    await this.testVaultRegistryLoading()
    await this.testPathServiceInitialization()
    await this.testContextVaultLoading()
    await this.testFileTreeLoading(contextId)
    await this.testPathResolution()

    return this.generateReport()
  }

  /**
   * Generate diagnostic report
   */
  generateReport(): LoadingSequenceReport {
    const totalDuration = Date.now() - this.startTime
    const issues: string[] = []
    const recommendations: string[] = []

    // Analyze results for issues
    const failedOperations = this.results.filter(r => !r.success)
    const slowOperations = this.results.filter(r => r.duration > 5000) // > 5 seconds

    if (failedOperations.length > 0) {
      issues.push(`${failedOperations.length} operations failed`)
      recommendations.push('Check error logs and implement proper error handling')
    }

    if (slowOperations.length > 0) {
      issues.push(`${slowOperations.length} operations were slow (>5s)`)
      recommendations.push('Optimize slow operations and implement caching')
    }

    // Check for race conditions
    const registryTime = this.results.find(r => r.operation === 'VaultRegistry Loading')?.timestamp || 0
    const pathServiceTime = this.results.find(r => r.operation === 'PathService Initialization')?.timestamp || 0
    
    if (pathServiceTime < registryTime) {
      issues.push('Path service initialized before vault registry loaded (race condition)')
      recommendations.push('Ensure proper initialization order: registry → path service → context service')
    }

    // Check for missing operations
    const expectedOperations = ['VaultRegistry Loading', 'PathService Initialization', 'ContextVault Loading', 'FileTree Loading']
    const completedOperations = this.results.map(r => r.operation)
    const missingOperations = expectedOperations.filter(op => !completedOperations.includes(op))

    if (missingOperations.length > 0) {
      issues.push(`Missing operations: ${missingOperations.join(', ')}`)
      recommendations.push('Ensure all required operations are executed in the loading sequence')
    }

    const report: LoadingSequenceReport = {
      totalDuration,
      operations: this.results,
      issues,
      recommendations
    }

    // Log report
    console.log('🔍 [DIAGNOSTICS] === DIAGNOSTIC REPORT ===')
    console.log(`🔍 [DIAGNOSTICS] Total Duration: ${totalDuration}ms`)
    console.log(`🔍 [DIAGNOSTICS] Operations: ${this.results.length}`)
    console.log(`🔍 [DIAGNOSTICS] Successful: ${this.results.filter(r => r.success).length}`)
    console.log(`🔍 [DIAGNOSTICS] Failed: ${failedOperations.length}`)
    
    if (issues.length > 0) {
      console.log('🔍 [DIAGNOSTICS] === ISSUES FOUND ===')
      issues.forEach(issue => console.log(`🔍 [DIAGNOSTICS] ❌ ${issue}`))
    }

    if (recommendations.length > 0) {
      console.log('🔍 [DIAGNOSTICS] === RECOMMENDATIONS ===')
      recommendations.forEach(rec => console.log(`🔍 [DIAGNOSTICS] 💡 ${rec}`))
    }

    console.log('🔍 [DIAGNOSTICS] === END REPORT ===')

    return report
  }

  /**
   * Monitor loading sequence in real-time
   */
  async monitorLoadingSequence(intervalMs: number = 1000, durationMs: number = 30000): Promise<void> {
    console.log(`🔍 [DIAGNOSTICS] Starting real-time monitoring for ${durationMs}ms...`)
    
    const startTime = Date.now()
    const interval = setInterval(async () => {
      const elapsed = Date.now() - startTime
      
      if (elapsed >= durationMs) {
        clearInterval(interval)
        console.log('🔍 [DIAGNOSTICS] Monitoring completed')
        return
      }

      console.log(`🔍 [DIAGNOSTICS] === MONITORING (${elapsed}ms) ===`)
      
      // Quick health check
      try {
        const registryCheck = await vaultUIManager.getVaultRegistry()
        console.log(`🔍 [DIAGNOSTICS] Registry: ${registryCheck ? '✅' : '❌'}`)
        
        const vaultsCheck = await contextVaultService.loadVaults()
        console.log(`🔍 [DIAGNOSTICS] Vaults: ${Array.isArray(vaultsCheck) ? '✅' : '❌'} (${vaultsCheck?.length || 0})`)
        
      } catch (error) {
        console.log(`🔍 [DIAGNOSTICS] Health check failed: ${error.message}`)
      }
    }, intervalMs)
  }
}

// Export singleton instance
export const vaultLoadingDiagnostics = new VaultLoadingDiagnostics()

// Convenience function for quick diagnostics
export async function runQuickDiagnostics(contextId?: string): Promise<LoadingSequenceReport> {
  return await vaultLoadingDiagnostics.runCompleteDiagnostics(contextId)
}

// Convenience function for monitoring
export async function startLoadingMonitor(intervalMs: number = 1000, durationMs: number = 30000): Promise<void> {
  return await vaultLoadingDiagnostics.monitorLoadingSequence(intervalMs, durationMs)
}
