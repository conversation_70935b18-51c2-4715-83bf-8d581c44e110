---
type: "agent_requested"
description: "Example description"
---

## 🚁 **AGENT COLLABORATION RULES**

### **Command System**
- **`/command`** - Start collaborative agent mode
- **`//hawk1`** - Analyst agent (current agent) - reports findings and plans
- **`//hawk2`** - Implementer agent - executes modifications and reports progress

### **Communication Protocol**
1. **hawk1** analyzes, plans, and reports findings
2. **hawk1** says "over" when finished
3. **hawk2** continues with implementation and reports work
4. **hawk2** says "over" when finished
5. **hawk1** analyzes results and provides next steps

### **Current Status**
**hawk1** - Ready for analysis and planning
**hawk2** - Awaiting implementation tasks

### **Recent Activity**
- **Priority 1 (Artifact Selection)** ✅ **COMPLETED** by hawk1
- **Next**: Priority 2 (Standardize File Operations) ready for hawk2

**hawk1 over**