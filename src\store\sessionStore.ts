import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { sessionRecoveryService } from '../services/sessionRecoveryService'

// Core session state interfaces
export interface VaultInfo {
  id: string
  name: string
  path: string
  color?: string
  icon?: string
}

export interface ContextInfo {
  id: string
  name: string
  path: string
  vaultId: string
  description?: string
}

export interface UserPrefs {
  theme: 'light' | 'dark' | 'system'
  layout: 'compact' | 'comfortable'
  autoSave: boolean
  notifications: boolean
}

export interface SessionState {
  // Core session state
  currentVault: VaultInfo | null
  currentContext: ContextInfo | null
  lastActivity: Date
  
  // User preferences
  userPreferences: UserPrefs
  
  // Session metadata
  sessionId: string
  createdAt: Date
  lastUpdated: Date
  
  // Actions
  setCurrentContext: (vault: VaultInfo, context: ContextInfo) => void
  setCurrentVault: (vault: VaultInfo) => void
  setCurrentContextOnly: (context: ContextInfo) => void
  clearSession: () => void
  updateUserPreferences: (prefs: Partial<UserPrefs>) => void
  getCurrentContext: () => ContextInfo | null
  getCurrentVault: () => VaultInfo | null
  isSessionActive: () => boolean
  updateLastActivity: () => void
}

// Default user preferences
const defaultUserPrefs: UserPrefs = {
  theme: 'system',
  layout: 'comfortable',
  autoSave: true,
  notifications: true
}

// Generate unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Create the session store with persistence
export const useSessionStore = create<SessionState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentVault: null,
      currentContext: null,
      lastActivity: new Date(),
      userPreferences: defaultUserPrefs,
      sessionId: generateSessionId(),
      createdAt: new Date(),
      lastUpdated: new Date(),

      // Set current context (vault + context)
      setCurrentContext: (vault: VaultInfo, context: ContextInfo) => {
        console.log('[SESSION] 🎯 Setting current context:', { vault: vault.name, context: context.name })
        
        set({
          currentVault: vault,
          currentContext: context,
          lastActivity: new Date(),
          lastUpdated: new Date()
        })

        // Update URL params for bookmarkable state
        updateURLParams(vault.id, context.id)
        
        // Store in localStorage for cross-tab sync
        localStorage.setItem('chatlo_current_vault', JSON.stringify(vault))
        localStorage.setItem('chatlo_current_context', JSON.stringify(context))
      },

      // Set current vault only
      setCurrentVault: (vault: VaultInfo) => {
        console.log('[SESSION] 🏦 Setting current vault:', vault.name)
        
        set({
          currentVault: vault,
          lastActivity: new Date(),
          lastUpdated: new Date()
        })

        // Update URL params
        updateURLParams(vault.id, get().currentContext?.id || '')
      },

      // Set current context only (within current vault)
      setCurrentContextOnly: (context: ContextInfo) => {
        const currentVault = get().currentVault
        if (!currentVault) {
          console.warn('[SESSION] ⚠️ Cannot set context without vault')
          return
        }

        console.log('[SESSION] 📁 Setting current context:', context.name)
        
        set({
          currentContext: context,
          lastActivity: new Date(),
          lastUpdated: new Date()
        })

        // Update URL params
        updateURLParams(currentVault.id, context.id)
      },

      // Clear session
      clearSession: () => {
        console.log('[SESSION] 🧹 Clearing session')
        
        set({
          currentVault: null,
          currentContext: null,
          lastActivity: new Date(),
          lastUpdated: new Date()
        })

        // Clear URL params
        clearURLParams()
        
        // Clear localStorage
        localStorage.removeItem('chatlo_current_vault')
        localStorage.removeItem('chatlo_current_context')
      },

      // Update user preferences
      updateUserPreferences: (prefs: Partial<UserPrefs>) => {
        set((state) => ({
          userPreferences: { ...state.userPreferences, ...prefs },
          lastUpdated: new Date()
        }))
      },

      // Get current context
      getCurrentContext: () => get().currentContext,

      // Get current vault
      getCurrentVault: () => get().currentVault,

      // Check if session is active
      isSessionActive: () => {
        const state = get()
        return !!(state.currentVault && state.currentContext)
      },

      // Update last activity timestamp
      updateLastActivity: () => {
        set({ lastActivity: new Date() })
      }
    }),
    {
      name: 'chatlo-session-store',
      partialize: (state) => ({
        userPreferences: state.userPreferences,
        sessionId: state.sessionId,
        createdAt: state.createdAt
      })
    }
  )
)

// URL parameter management
function updateURLParams(vaultId: string, contextId: string) {
  try {
    const url = new URL(window.location.href)
    url.searchParams.set('vault', vaultId)
    url.searchParams.set('context', contextId)
    
    // Use replaceState to avoid adding to browser history
    window.history.replaceState({}, '', url.toString())
    
    console.log('[SESSION] 🔗 Updated URL params:', { vaultId, contextId })
  } catch (error) {
    console.warn('[SESSION] ⚠️ Failed to update URL params:', error)
  }
}

function clearURLParams() {
  try {
    const url = new URL(window.location.href)
    url.searchParams.delete('vault')
    url.searchParams.delete('context')
    
    window.history.replaceState({}, '', url.toString())
    
    console.log('[SESSION] 🔗 Cleared URL params')
  } catch (error) {
    console.warn('[SESSION] ⚠️ Failed to clear URL params:', error)
  }
}

// Enhanced session recovery utilities
export const sessionRecovery = {
  // Get context from URL params
  getContextFromURL: (): { vaultId: string; contextId: string } | null => {
    try {
      const url = new URL(window.location.href)
      const vaultId = url.searchParams.get('vault')
      const contextId = url.searchParams.get('context')
      
      if (vaultId && contextId) {
        console.log('[SESSION-RECOVERY] 🔗 Found context in URL:', { vaultId, contextId })
        return { vaultId, contextId }
      }
      
      return null
    } catch (error) {
      console.warn('[SESSION-RECOVERY] ⚠️ Failed to parse URL params:', error)
      return null
    }
  },

  // Get stored context from localStorage
  getStoredContext: (): { vault: VaultInfo; context: ContextInfo } | null => {
    try {
      const vaultStr = localStorage.getItem('chatlo_current_vault')
      const contextStr = localStorage.getItem('chatlo_current_context')
      
      if (vaultStr && contextStr) {
        const vault = JSON.parse(vaultStr) as VaultInfo
        const context = JSON.parse(contextStr) as ContextInfo
        
        console.log('[SESSION-RECOVERY] 💾 Found stored context:', { vault: vault.name, context: context.name })
        return { vault, context }
      }
      
      return null
    } catch (error) {
      console.warn('[SESSION-RECOVERY] ⚠️ Failed to parse stored context:', error)
      return null
    }
  },

  // Initialize session with enhanced recovery
  initializeSession: async (): Promise<void> => {
    console.log('[SESSION-RECOVERY] 🚀 Initializing session with enhanced recovery...')
    
    // Try URL params first (highest priority - bookmarkable)
    const urlContext = sessionRecovery.getContextFromURL()
    if (urlContext) {
      console.log('[SESSION-RECOVERY] 🎯 Resolving context from URL params...')
      
      try {
        const resolved = await sessionRecoveryService.resolveContextFromIds(
          urlContext.vaultId, 
          urlContext.contextId
        )
        
        if (resolved) {
          useSessionStore.getState().setCurrentContext(resolved.vault, resolved.context)
          console.log('[SESSION-RECOVERY] ✅ Session restored from URL params')
          return
        } else {
          console.warn('[SESSION-RECOVERY] ⚠️ Failed to resolve context from URL params, falling back...')
        }
      } catch (error) {
        console.error('[SESSION-RECOVERY] ❌ Error resolving context from URL params:', error)
      }
    }
    
    // Try localStorage (second priority - persistent)
    const storedContext = sessionRecovery.getStoredContext()
    if (storedContext) {
      // Validate stored context against registry
      try {
        const isValid = await sessionRecoveryService.validateContextIds(
          storedContext.vault.id, 
          storedContext.context.id
        )
        
        if (isValid) {
          useSessionStore.getState().setCurrentContext(storedContext.vault, storedContext.context)
          console.log('[SESSION-RECOVERY] ✅ Session restored from localStorage')
          return
        } else {
          console.warn('[SESSION-RECOVERY] ⚠️ Stored context is invalid, falling back...')
          // Clear invalid stored context
          localStorage.removeItem('chatlo_current_vault')
          localStorage.removeItem('chatlo_current_context')
        }
      } catch (error) {
        console.error('[SESSION-RECOVERY] ❌ Error validating stored context:', error)
      }
    }
    
    // Fallback to default context (last resort)
    console.log('[SESSION-RECOVERY] 🔄 No valid context found, using default...')
    try {
      const defaultContext = await sessionRecoveryService.getDefaultContext()
      if (defaultContext) {
        useSessionStore.getState().setCurrentContext(defaultContext.vault, defaultContext.context)
        console.log('[SESSION-RECOVERY] ✅ Session initialized with default context')
      } else {
        console.warn('[SESSION-RECOVERY] ⚠️ No default context available')
      }
    } catch (error) {
      console.error('[SESSION-RECOVERY] ❌ Failed to get default context:', error)
    }
  }
}

// Export default instance for direct access
export default useSessionStore
