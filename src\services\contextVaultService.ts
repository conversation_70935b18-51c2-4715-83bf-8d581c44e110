import { ContextVault, ContextFolder } from '../types'
import { vaultUIManager } from './vaultUIManager'
import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { vault, events } from '../api/UnifiedAPIClient'

// Shared dropbox context ID constant
export const SHARED_DROPBOX_CONTEXT_ID = 'shared-dropbox'

// Simple toast replacement for missing toastService
const toastService = {
  error: (title: string, message: string) => {
    console.error(`${title}: ${message}`)
  },
  contextCreated: (name: string, type: string) => {
    console.log(`Context created: ${name} (${type})`)
  },
  contextCreationFailed: (error: string) => {
    console.error(`Context creation failed: ${error}`)
  },
  vaultSyncCompleted: (count: number) => {
    console.log(`Vault sync completed: ${count} contexts`)
  }
}

export interface CreateContextOptions {
  name: string
  objective?: string
  vaultType: 'Personal' | 'Work'
  color?: string
  icon?: string
}

export interface ContextVaultChangeListener {
  (vaults: ContextVault[], selectedContextId?: string): void
}

class ContextVaultService extends BaseService {
  private listeners: ContextVaultChangeListener[] = []
  private currentVaults: ContextVault[] = []
  private selectedContextId: string | null = null
  private explicitlyCleared: boolean = false // Track if user explicitly cleared context
  private readonly STORAGE_KEY = 'chatlo_selected_context_id'
  private unsubscribeVaultEvent?: () => void
  
  // State management optimization properties
  private vaultChangeCoalesceTimer: NodeJS.Timeout | null = null
  private pendingVaultChanges: Set<string> = new Set()

  constructor() {
    super({
      name: 'ContextVaultService',
      autoInitialize: true
    })
  }

  /**
   * Subscribe to context vault changes
   */
  subscribe(listener: ContextVaultChangeListener) {
    this.listeners.push(listener)
    // Immediately call with current data
    listener(this.currentVaults, this.selectedContextId || undefined)
    
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**
   * Notify all listeners of changes
   */
  private notify() {
    this.listeners.forEach(listener => 
      listener(this.currentVaults, this.selectedContextId || undefined)
    )
  }

  /**
   * Load all vaults and contexts
   */
  async loadVaults(): Promise<ContextVault[]> {
    const result = await this.executeOperation(
      'loadVaults',
      async () => {
        const registry = await vaultUIManager.getVaultRegistry()
        if (!registry) {
          return []
        }

        this.currentVaults = registry.vaults

        // Restore selected context from localStorage if not already set
        if (!this.selectedContextId) {
          await this.restoreSelectedContext()
        }

        this.notify()
        return registry.vaults
      }
    )

    if (!result.success) {
      this.logger.error('Failed to load vaults', 'loadVaults', result.error)
      toastService.error('Failed to Load Vaults', 'Could not load context vaults')
      return []
    }

    return result.data!
  }

  /**
   * Restore selected context from localStorage
   */
  private async restoreSelectedContext(): Promise<void> {
    const savedContextId = localStorage.getItem(this.STORAGE_KEY)
    if (savedContextId) {
      const result = this.findContextById(savedContextId)
      if (result) {
        this.selectedContextId = savedContextId
      } else {
        // Context no longer exists, clear from storage
        localStorage.removeItem(this.STORAGE_KEY)
      }
    }

    // If still no selected context and not explicitly cleared, auto-select the most recently accessed one
    if (!this.selectedContextId && !this.explicitlyCleared && this.currentVaults.length > 0) {
      const allContexts = this.getAllContexts()
      if (allContexts.length > 0) {
        // Sort by last modified and select the most recent
        const sortedContexts = allContexts.sort((a, b) =>
          new Date(b.stats.lastModified).getTime() - new Date(a.stats.lastModified).getTime()
        )
        this.selectedContextId = sortedContexts[0].id
        localStorage.setItem(this.STORAGE_KEY, this.selectedContextId)
      }
    }
  }

  /**
   * Get current vaults (cached)
   */
  getCurrentVaults(): ContextVault[] {
    return this.currentVaults
  }

  /**
   * Get all contexts from all vaults
   */
  getAllContexts(): ContextFolder[] {
    const contexts: ContextFolder[] = []
    for (const vault of this.currentVaults) {
      contexts.push(...vault.contexts)
    }
    return contexts
  }

  /**
   * Find context by ID
   */
  findContextById(contextId: string): { context: ContextFolder; vault: ContextVault } | null {
    for (const vault of this.currentVaults) {
      const context = vault.contexts.find(c => c.id === contextId)
      if (context) {
        return { context, vault }
      }
    }
    return null
  }

  /**
   * Set selected context
   */
  setSelectedContext(contextId: string | null) {
    this.selectedContextId = contextId

    // Track if this is an explicit clear (shared dropbox selection) vs explicit selection
    if (contextId === null) {
      this.explicitlyCleared = true
    } else {
      this.explicitlyCleared = false // Reset when explicitly selecting a context
    }

    // Persist to localStorage
    if (contextId) {
      localStorage.setItem(this.STORAGE_KEY, contextId)
    } else {
      localStorage.removeItem(this.STORAGE_KEY)
    }

    this.notify()
  }

  /**
   * Clear selected context
   */
  clearSelectedContext() {
    this.setSelectedContext(null)
  }

  /**
   * Get selected context
   */
  getSelectedContext(): ContextFolder | null {
    if (!this.selectedContextId) return null
    const result = this.findContextById(this.selectedContextId)
    return result?.context || null
  }

  /**
   * Get selected context ID
   */
  getSelectedContextId(): string | null {
    return this.selectedContextId
  }

  /**
   * Create a new context in the specified vault
   */
  async createContext(options: CreateContextOptions): Promise<{ success: boolean; context?: ContextFolder; error?: string }> {
    const result = await this.executeOperation(
      'createContext',
      async () => {
        // Find the target vault based on vaultType
        const targetVault = this.currentVaults.find(v =>
          v.name.toLowerCase().includes(options.vaultType.toLowerCase())
        )

        if (!targetVault) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `${options.vaultType} vault not found`,
            { serviceName: this.serviceName, operation: 'createContext', details: { vaultType: options.vaultType } }
          )
        }

        // 1. Determine the folder name. Use sanitized names for all contexts.
        // CRITICAL FIX: Remove hardcoded fallbacks, make all names dynamic
        const userFriendlyFolderName = options.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '') // Remove invalid chars
          .replace(/\s+/g, '-')         // Replace spaces with hyphens
          .replace(/-+/g, '-')         // Replace multiple hyphens with single hyphen
          .replace(/^-+|-+$/g, '')    // Remove leading/trailing hyphens

        // 2. Construct the context folder path
        const contextFolderPath = `${targetVault.path}/${userFriendlyFolderName}`

        // 3. Check if the folder already exists
        const pathExists = await vault.pathExists(contextFolderPath)
        if (pathExists.success && pathExists.data === true) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `A context with the name '${options.name}' already exists`,
            { serviceName: this.serviceName, operation: 'createContext', details: { contextPath: contextFolderPath } }
          )
        }

        // 3. Generate a unique system ID for tracking the context in metadata.
        const uniqueContextId = `ctx_${Math.random().toString(36).substr(2, 9)}_${Math.random().toString(36).substr(2, 9)}`;

        // 4. Create the directory structure using the user-friendly path.
        await this.createContextDirectories(contextFolderPath);

        // 5. Create the master file inside the new folder.
        await this.createMasterFile(contextFolderPath, options);

        // 6. Create the metadata file, storing the unique ID and the folder name.
        await this.createContextMetadata(contextFolderPath, uniqueContextId, options, userFriendlyFolderName);

        // Rescan vaults and update registry to include the new context (standard IPC registry update)
        const updatedRegistry = await vaultUIManager.scanVaults()
        if (updatedRegistry) {
          this.currentVaults = updatedRegistry.vaults
          this.notify()
        }

        // Find the newly created context by path, as ID is not part of the folder name
        const newContextResult = this.findContextByPath(contextFolderPath);
        if (newContextResult) {
          // Set as selected context
          this.setSelectedContext(newContextResult.context.id) // Use the ID from the found context

          // Show success toast
          toastService.contextCreated(options.name, options.vaultType)

          return { success: true, context: newContextResult.context }
        }

        return { success: true }
      },
      { contextName: options.name, vaultType: options.vaultType }
    )

    if (!result.success) {
      const errorMessage = result.error!.getUserMessage()
      toastService.error('Failed to Create Context', errorMessage)
      return { success: false, error: errorMessage }
    }

    return result.data!
  }

  /**
   * Create context directory structure
   */
  private async createContextDirectories(contextPath: string): Promise<void> {
    // Create main context directory
    const createDirResult = await vault.createDirectory(contextPath)
    if (!createDirResult.success) {
      throw new ServiceError(
        ServiceErrorCode.FILE_WRITE_ERROR,
        `Failed to create context directory: ${createDirResult.error}`,
        { serviceName: this.serviceName, operation: 'createContextDirectories', details: { contextPath } }
      )
    }

    // Create subdirectories
    const subdirs = ['documents', 'images', 'artifacts', '.intelligence']
    for (const subdir of subdirs) {
      const subdirResult = await vault.createDirectory(`${contextPath}/${subdir}`)
      if (!subdirResult.success) {
        throw new ServiceError(
          ServiceErrorCode.FILE_WRITE_ERROR,
          `Failed to create subdirectory ${subdir}: ${subdirResult.error}`,
          { serviceName: this.serviceName, operation: 'createContextDirectories', details: { contextPath, subdir } }
        )
      }
    }
  }

  /**
   * Create master.md file for context
   */
  private async createMasterFile(contextPath: string, options: CreateContextOptions): Promise<void> {
    const masterContent = options.objective
      ? `# ${options.name}\n\n## Objective\n${options.objective}\n\n## Getting Started\nThis is your new context vault. You can:\n- Add documents to the documents folder\n- Upload images to the images folder\n- Generate artifacts that will be saved to the artifacts folder\n\nStart chatting with AI to begin working with your context!`
      : `# ${options.name}\n\n## Getting Started\nThis is your new context vault. You can:\n- Add documents to the documents folder\n- Upload images to the images folder\n- Generate artifacts that will be saved to the artifacts folder\n\nStart chatting with AI to begin working with your context!`

    const writeResult = await vault.writeFile(`${contextPath}/master.md`, masterContent)
    if (!writeResult.success) {
      throw new ServiceError(
        ServiceErrorCode.FILE_WRITE_ERROR,
        `Failed to create master file: ${writeResult.error}`,
        { serviceName: this.serviceName, operation: 'createMasterFile', details: { contextPath } }
      )
    }
  }

  /**
   * Create context metadata file
   */
  private async createContextMetadata(contextPath: string, contextId: string, options: CreateContextOptions, userFriendlyFolderName: string): Promise<void> {
    const metadata = {
      id: contextId,
      name: options.name,
      folderName: userFriendlyFolderName,
      created: new Date().toISOString(),
      description: options.objective || `Context vault for ${options.name}`,
      color: options.color || (options.vaultType === 'Personal' ? '#8AB0BB' : '#FF8383'),
      icon: options.icon || 'fa-folder',
      contextType: 'project'
    }

    const writeResult = await vault.writeFile(
      `${contextPath}/.intelligence/metadata.json`,
      JSON.stringify(metadata, null, 2)
    )

    if (!writeResult.success) {
      throw new ServiceError(
        ServiceErrorCode.FILE_WRITE_ERROR,
        `Failed to create metadata file: ${writeResult.error}`,
        { serviceName: this.serviceName, operation: 'createContextMetadata', details: { contextPath } }
      )
    }
  }

  /**
   * Find a context by its folder path.
   */
  findContextByPath(path: string): { vault: ContextVault; context: ContextFolder } | null {
    for (const vault of this.currentVaults) {
      for (const context of vault.contexts) {
        if (context.path === path) {
          return { vault, context };
        }
      }
    }
    return null;
  }

  /**
   * Refresh vault data (rescan all vaults)
   */
  async refreshVaults(): Promise<void> {
    const result = await this.executeOperation(
      'refreshVaults',
      async () => {
        // Get current vault root for logging
        const registry = await vaultUIManager.getVaultRegistry()
        const vaultRoot = registry?.vaultRoot || 'unknown'

        this.logger.info('Starting vault refresh', 'refreshVaults', { vaultRoot })

        const updatedRegistry = await vaultUIManager.scanVaults()
        if (updatedRegistry) {
          this.currentVaults = updatedRegistry.vaults
          this.notify()

          const contextCount = this.getAllContexts().length
          toastService.vaultSyncCompleted(contextCount)

          this.logger.info('Vaults refreshed successfully', 'refreshVaults', {
            vaultRoot: updatedRegistry.vaultRoot,
            vaultCount: this.currentVaults.length,
            contextCount
          })
        }
      }
    )

    if (!result.success) {
      this.logger.error('Failed to refresh vaults', 'refreshVaults', result.error)
      toastService.error('Sync Failed', 'Could not refresh vault data')
    }
  }

  /**
   * Ensure service is initialized and vaults are loaded
   */
  async ensureInitialized(): Promise<void> {
    if (!this.getStatus().initialized) {
      await this.initialize()
    } else if (this.currentVaults.length === 0) {
      // Service is initialized but vaults might not be loaded
      await this.loadVaults()
    }
  }

  /**
   * Initialize the service (load initial data)
   */
  protected async doInitialize(): Promise<void> {
    await this.loadVaults()
    // ✅ MIGRATED: Listen for vault root changes and refresh using UnifiedAPIClient
    try {
      // Subscribe to vault events using UnifiedAPIClient
      const subscription = await events.subscribe('vault', { eventNames: ['vault:rootChanged'] })
      if (subscription?.subscriptionId) {
        const channel = `events:subscription:${subscription.subscriptionId}`
        this.unsubscribeVaultEvent = (window as any).electronAPI?.events?.on
          ? (window as any).electronAPI.events.on(channel, async (payload: any) => {
              this.logger.info('Detected vault:rootChanged event - refreshing vaults', 'vault:rootChanged', payload)
              // Use coalesced vault change handler for better performance
              await this.handleCoalescedVaultChange(payload)
            })
          : () => {}
      }
    } catch (e) {
      this.logger.warn('Failed to attach vault:rootChanged listener', 'doInitialize', e as Error)
    }
    this.logger.info('Context vault service initialized successfully', 'doInitialize', {
      vaultCount: this.currentVaults.length,
      contextCount: this.getAllContexts().length,
      selectedContextId: this.selectedContextId
    })
  }

  /**
   * Handle vault changes with coalescing to prevent rapid successive refreshes
   */
  private async handleCoalescedVaultChange(payload: any): Promise<void> {
    // Coalesce rapid vault changes
    if (this.vaultChangeCoalesceTimer) {
      clearTimeout(this.vaultChangeCoalesceTimer)
    }
    
    const vaultRoot = payload?.vaultRoot || 'unknown'
    this.pendingVaultChanges.add(vaultRoot)
    
    this.vaultChangeCoalesceTimer = setTimeout(async () => {
      try {
        // Process all pending changes in one batch
        const changes = Array.from(this.pendingVaultChanges)
        this.pendingVaultChanges.clear()
        
        this.logger.info('Processing coalesced vault changes', 'handleCoalescedVaultChange', { 
          changes, 
          totalChanges: changes.length 
        })
        
        // Single refresh for all changes
        const previousSelected = this.selectedContextId
        await this.refreshVaults()
        
        // Preserve selection if still valid
        if (previousSelected && !this.findContextById(previousSelected)) {
          this.setSelectedContext(null)
        }
        
        // Emit single event for all changes
        await events.emit('vault:refreshed', { vaultRoots: changes })
        
      } catch (error) {
        this.logger.error('Failed to process coalesced vault changes', 'handleCoalescedVaultChange', error)
      } finally {
        this.vaultChangeCoalesceTimer = null
      }
    }, 300) // 300ms coalescing window
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      // Check if we can access the vault registry
      const registry = await vaultUIManager.getVaultRegistry()
      return registry !== null
    } catch (error) {
      this.logger.warn('Health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.listeners = []
    this.currentVaults = []
    this.selectedContextId = null
    this.explicitlyCleared = false
    try {
      if (this.unsubscribeVaultEvent) {
        this.unsubscribeVaultEvent()
        this.unsubscribeVaultEvent = undefined
      }
    } catch {}
    this.logger.info('Context vault service cleaned up', 'doCleanup')
  }

  /**
   * Public initialization method for backward compatibility
   */
  public async initialize(): Promise<void> {
    return super.initialize()
  }
}

export const contextVaultService = new ContextVaultService()
