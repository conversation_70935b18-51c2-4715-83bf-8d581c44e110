import React, { createContext, useContext, useEffect, ReactNode } from 'react'
import { useSessionStore, sessionRecovery, VaultInfo, ContextInfo } from '../store/sessionStore'

// Session context interface
interface SessionContextType {
  // Current session state
  currentVault: VaultInfo | null
  currentContext: ContextInfo | null
  isSessionActive: boolean
  
  // Session actions
  setCurrentContext: (vault: VaultInfo, context: ContextInfo) => void
  setCurrentVault: (vault: VaultInfo) => void
  setCurrentContextOnly: (context: ContextInfo) => void
  clearSession: () => void
  
  // Session recovery
  initializeSession: () => Promise<void>
  isInitializing: boolean
  
  // User preferences
  userPreferences: {
    theme: 'light' | 'dark' | 'system'
    layout: 'compact' | 'comfortable'
    autoSave: boolean
    notifications: boolean
  }
  updateUserPreferences: (prefs: Partial<{
    theme: 'light' | 'dark' | 'system'
    layout: 'compact' | 'comfortable'
    autoSave: boolean
    notifications: boolean
  }>) => void
}

// Create the context
const SessionContext = createContext<SessionContextType | undefined>(undefined)

// Session provider props
interface SessionProviderProps {
  children: ReactNode
  onSessionReady?: (vault: VaultInfo, context: ContextInfo) => void
  onSessionError?: (error: Error) => void
}

// Session provider component
export const SessionProvider: React.FC<SessionProviderProps> = ({ 
  children, 
  onSessionReady, 
  onSessionError 
}) => {
  // Get session state from store
  const currentVault = useSessionStore(state => state.currentVault)
  const currentContext = useSessionStore(state => state.currentContext)
  const isSessionActive = useSessionStore(state => state.isSessionActive())
  const userPreferences = useSessionStore(state => state.userPreferences)
  
  // Get session actions from store
  const setCurrentContext = useSessionStore(state => state.setCurrentContext)
  const setCurrentVault = useSessionStore(state => state.setCurrentVault)
  const setCurrentContextOnly = useSessionStore(state => state.setCurrentContextOnly)
  const clearSession = useSessionStore(state => state.clearSession)
  const updateUserPreferences = useSessionStore(state => state.updateUserPreferences)
  
  // Local state for initialization
  const [isInitializing, setIsInitializing] = React.useState(true)
  const [hasInitialized, setHasInitialized] = React.useState(false)

  // Initialize session on mount
  useEffect(() => {
    if (hasInitialized) return

    const initializeSession = async () => {
      try {
        setIsInitializing(true)
        console.log('[SESSION-PROVIDER] 🚀 Initializing session...')
        
        await sessionRecovery.initializeSession()
        
        // Check if we have a valid session
        const store = useSessionStore.getState()
        if (store.isSessionActive()) {
          const vault = store.getCurrentVault()!
          const context = store.getCurrentContext()!
          
          console.log('[SESSION-PROVIDER] ✅ Session initialized successfully:', {
            vault: vault.name,
            context: context.name
          })
          
          // Notify parent component
          onSessionReady?.(vault, context)
        } else {
          console.log('[SESSION-PROVIDER] ℹ️ No active session found')
        }
        
        setHasInitialized(true)
      } catch (error) {
        console.error('[SESSION-PROVIDER] ❌ Failed to initialize session:', error)
        onSessionError?.(error as Error)
      } finally {
        setIsInitializing(false)
      }
    }

    initializeSession()
  }, [hasInitialized, onSessionReady, onSessionError])

  // Watch for session changes and notify parent
  useEffect(() => {
    if (currentVault && currentContext && onSessionReady) {
      onSessionReady(currentVault, currentContext)
    }
  }, [currentVault, currentContext, onSessionReady])

  // Context value
  const contextValue: SessionContextType = {
    currentVault,
    currentContext,
    isSessionActive,
    setCurrentContext,
    setCurrentVault,
    setCurrentContextOnly,
    clearSession,
    initializeSession: sessionRecovery.initializeSession,
    isInitializing,
    userPreferences,
    updateUserPreferences
  }

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  )
}

// Custom hook to use session context
export const useSession = (): SessionContextType => {
  const context = useContext(SessionContext)
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider')
  }
  return context
}

// Export the context for direct access if needed
export { SessionContext }
export default SessionProvider
